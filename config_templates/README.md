# 配置文件使用说明

## 配置文件结构

配置文件 `extract_config.json` 用于定义文档主题和需要提取的字段。每个配置文件包含以下结构：

### 主题配置 (themes)

每个主题包含以下属性：

- **name**: 主题名称，用于标识主题类型
- **id**: 主题唯一标识符
- **fields**: 该主题下需要提取的字段配置

### 字段配置 (fields)

每个字段包含以下属性：

- **regex**: 用于提取字段值的正则表达式
- **description**: 字段描述信息

## 字段提取逻辑

### 1. 主题检测
系统会根据以下逻辑选择主题：
- 如果配置文件中只有一个主题，直接使用该主题
- 如果有多个主题，尝试根据主题名称在文档中的出现情况选择最匹配的主题
- 如果没有找到匹配的主题，使用第一个主题作为默认选择

### 2. 字段提取优先级
按照以下优先级提取字段内容：

1. **配置文件正则表达式** (最高优先级)
   - 如果有捕获组 `()`，提取第一个捕获组内容
   - 如果没有捕获组，提取整个匹配内容

2. **OCR行提取**
   - 当正则表达式匹配失败时使用
   - 查找包含字段名的文本行

3. **LayoutLM模型提取**
   - 当其他方法都失败时使用
   - 基于预训练模型的结构化提取

## 正则表达式编写规则

### 基本格式
```
字段名[:：]?\\s*([^\\n\\s]+)
```

### 说明
- `字段名`: 要匹配的字段名称
- `[:：]?`: 可选的冒号（支持中英文冒号）
- `\\s*`: 可选的空白字符
- `([^\\n\\s]+)`: 捕获组，匹配字段值（不包含换行符和空格）

### 特殊字段格式

#### 日期字段
```json
"检查日期": {
  "regex": "检查日期[\\s:：;；\\^\\-]*([0-9]{4}[\\/\\-][0-9]{1,4}[\\/\\-][0-9]{1,2})",
  "description": "检查日期"
}
```

#### 编号字段
```json
"病案号": {
  "regex": "[病痛][历案]?[号码][\\s:：;；\\^\\-]*([A-Za-z0-9]+)",
  "description": "病案号或病历号"
}
```

#### 选择字段
```json
"送检科室": {
  "regex": "(神经内科门诊|神经内科|内科门诊|放射科|核医学科)",
  "description": "送检科室名称"
}
```

## 配置示例

### PET检查报告主题
```json
{
  "name": "PET检查报告",
  "id": "1",
  "fields": {
    "姓名": {
      "regex": "姓名[:：]?\\s*([^\\n\\s]+)",
      "description": "患者姓名"
    },
    "检查项目": {
      "regex": "检查项目[:：]?\\s*([^\\n\\s]+)",
      "description": "医学检查项目名称"
    }
  }
}
```

### 通用医学报告主题
```json
{
  "name": "通用医学报告",
  "id": "2",
  "fields": {
    "送检科室": {
      "regex": "(神经内科门诊|神经内科|内科门诊|放射科|核医学科)",
      "description": "送检科室名称"
    }
  }
}
```

## 常见问题

### 1. 字段提取失败
- 检查正则表达式是否正确
- 确保正则表达式包含捕获组 `()`
- 验证字段名是否与实际文档中的字段名一致
- 检查字段值是否包含在匹配范围内

### 2. 主题选择问题
- 确保主题名称清晰明确
- 如果只有一个主题，系统会自动使用该主题
- 如果有多个主题，主题名称应该具有区分性

### 3. 字段值不完整
- 调整正则表达式的匹配范围
- 使用 `[^\\n\\s]+` 而不是 `[^\\n]+` 来避免匹配到后续字段
- 对于多行字段，考虑使用更复杂的正则表达式

### 4. 正则表达式语法错误
- 确保转义字符正确（如 `\\s`, `\\n`）
- 检查括号匹配
- 验证特殊字符的转义

## 测试配置

可以使用以下命令测试配置是否正确：

```bash
python -c "
import json
from structured_report.extractor.config_loader import load_config
from structured_report.extractor.theme_detector import detect_theme

config = load_config('structured_report/extract_config.json')
print('配置加载成功')
print(f'主题数量: {len(config[\"themes\"])}')

# 测试主题检测
test_text = 'PET检查报告 患者姓名：张三 检查项目：PET-CT'
theme = detect_theme(test_text, config)
if theme:
    print(f'检测到主题: {theme[\"name\"]}')
    print(f'字段数量: {len(theme[\"fields\"])}')
else:
    print('未检测到主题')
"
```

## 最佳实践

1. **主题命名**：使用清晰、具有区分性的主题名称
2. **字段命名**：使用清晰、一致的字段名称
3. **正则优化**：优先使用捕获组来精确提取字段值
4. **测试验证**：使用实际文档内容测试配置效果
5. **版本管理**：保持配置文件的版本控制和备份
