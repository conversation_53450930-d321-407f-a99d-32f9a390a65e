#!/usr/bin/env python3
"""
结构化医学报告API客户端测试示例
测试所有API接口的功能
"""

import os
import requests
import json
import time

# API基础URL
BASE_URL = "http://localhost:5000"

def test_health_check():
    """测试健康检查接口"""
    print("测试健康检查接口...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            print("✅ 健康检查通过")
            print(f"   状态: {response.json().get('status')}")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")

def test_get_config():
    """测试获取配置文件接口"""
    print("\n测试获取配置文件接口...")
    try:
        response = requests.get(f"{BASE_URL}/config")
        if response.status_code == 200:
            data = response.json()
            print("✅ 获取配置成功")
            print(f"   配置文件: {data.get('config_file')}")
            print(f"   主题数量: {len(data.get('config', {}).get('themes', []))}")
        else:
            print(f"❌ 获取配置失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
    except Exception as e:
        print(f"❌ 获取配置异常: {e}")

def test_update_config():
    """测试更新配置文件接口"""
    print("\n测试更新配置文件接口...")
    try:
        # 创建一个测试配置
        test_config = {
            "themes": [
                {
                    "name": "TEST_THEME",
                    "match_keywords": ["测试", "TEST"],
                    "fields": {
                        "测试字段": {"regex": "测试[:：]?\\s*([^\\n]+)"}
                    }
                }
            ]
        }
        
        response = requests.put(
            f"{BASE_URL}/config",
            json=test_config,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 更新配置成功")
            print(f"   备份文件: {data.get('backup')}")
        else:
            print(f"❌ 更新配置失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
    except Exception as e:
        print(f"❌ 更新配置异常: {e}")

def test_list_config_backups():
    """测试获取配置备份列表接口"""
    print("\n测试获取配置备份列表接口...")
    try:
        response = requests.get(f"{BASE_URL}/config/backup")
        if response.status_code == 200:
            data = response.json()
            print("✅ 获取备份列表成功")
            print(f"   备份数量: {data.get('total_count')}")
            print(f"   备份目录: {data.get('backup_dir')}")
            
            if data.get('backups'):
                latest_backup = data['backups'][0]
                print(f"   最新备份: {latest_backup['filename']}")
                print(f"   创建时间: {latest_backup['created_time']}")
        else:
            print(f"❌ 获取备份列表失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
    except Exception as e:
        print(f"❌ 获取备份列表异常: {e}")

def test_restore_config():
    """测试恢复配置文件接口"""
    print("\n测试恢复配置文件接口...")
    try:
        # 先获取备份列表
        response = requests.get(f"{BASE_URL}/config/backup")
        if response.status_code == 200:
            data = response.json()
            if data.get('backups'):
                latest_backup = data['backups'][0]['filename']
                
                # 恢复配置
                restore_data = {"backup_file": latest_backup}
                response = requests.post(
                    f"{BASE_URL}/config/restore",
                    json=restore_data,
                    headers={'Content-Type': 'application/json'}
                )
                
                if response.status_code == 200:
                    data = response.json()
                    print("✅ 恢复配置成功")
                    print(f"   恢复自: {data.get('restored_from')}")
                else:
                    print(f"❌ 恢复配置失败: {response.status_code}")
                    print(f"   错误信息: {response.text}")
            else:
                print("⚠️  没有可用的备份文件")
        else:
            print(f"❌ 获取备份列表失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 恢复配置异常: {e}")

def test_extract_document(image_path):
    """测试文档提取接口"""
    print("\n测试文档提取接口...")
    try:
        with open(image_path, 'rb') as f:
            files = {'file': f}
            response = requests.post(f"{BASE_URL}/extract", files=files)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 文档提取成功")
            print(f"   任务ID: {data.get('task_id')}")
            print(f"   提取字段数: {len(data.get('fields', {}))}")
            return data.get('task_id')
        else:
            print(f"❌ 文档提取失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 文档提取异常: {e}")
        return None

def test_modify_fields(image_path, modifications):
    """测试字段修改接口"""
    print(f"\n测试字段修改接口...")
    try:
        with open(image_path, 'rb') as f:
            files = {'file': f}
            data = {'modifications': json.dumps(modifications)}
            response = requests.post(f"{BASE_URL}/modify", files=files, data=data)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 字段修改成功")
            print(f"   任务ID: {data.get('task_id')}")
            print(f"   修改字段: {list(modifications.keys())}")
            return data.get('task_id')
        else:
            print(f"❌ 字段修改失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 字段修改异常: {e}")
        return None

def test_apply_mosaic(image_path, mosaic_fields):
    """测试马赛克处理接口"""
    print(f"\n测试马赛克处理接口...")
    try:
        with open(image_path, 'rb') as f:
            files = {'file': f}
            data = {'fields': json.dumps(mosaic_fields)}
            response = requests.post(f"{BASE_URL}/mosaic", files=files, data=data)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 马赛克处理成功")
            print(f"   任务ID: {data.get('task_id')}")
            print(f"   马赛克字段: {mosaic_fields}")
            return data.get('task_id')
        else:
            print(f"❌ 马赛克处理失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 马赛克处理异常: {e}")
        return None

def test_comprehensive_process(image_path, modifications, mosaic_fields):
    """测试综合处理接口"""
    print(f"\n测试综合处理接口...")
    try:
        with open(image_path, 'rb') as f:
            files = {'file': f}
            data = {
                'modifications': json.dumps(modifications),
                'mosaic_fields': json.dumps(mosaic_fields)
            }
            response = requests.post(f"{BASE_URL}/process", files=files, data=data)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 综合处理成功")
            print(f"   任务ID: {data.get('task_id')}")
            print(f"   修改字段: {list(modifications.keys())}")
            print(f"   马赛克字段: {mosaic_fields}")
            return data.get('task_id')
        else:
            print(f"❌ 综合处理失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 综合处理异常: {e}")
        return None

def download_file(task_id, filename, local_filename):
    """下载文件"""
    try:
        response = requests.get(f"{BASE_URL}/download/{task_id}/{filename}")
        if response.status_code == 200:
            with open(local_filename, 'wb') as f:
                f.write(response.content)
            print(f"✅ 文件下载成功: {local_filename}")
            return True
        else:
            print(f"❌ 文件下载失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 文件下载异常: {e}")
        return False

def cleanup_task(task_id):
    """清理任务临时文件"""
    try:
        response = requests.delete(f"{BASE_URL}/cleanup/{task_id}")
        if response.status_code == 200:
            print(f"✅ 任务清理成功: {task_id}")
        else:
            print(f"❌ 任务清理失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 任务清理异常: {e}")

def main():
    print("开始测试结构化医学报告API")
    print("=" * 50)
    
    # 检查测试图片
    test_image = "uploads/2.jpg"
    if not os.path.exists(test_image):
        print(f"测试图片不存在: {test_image}")
        return
    
    # 测试基础接口
    test_health_check()
    
    # 测试配置管理接口
    test_get_config()
    test_update_config()
    test_list_config_backups()
    test_restore_config()
    
    # 测试配置模板管理接口
    test_list_config_templates()
    test_save_config_template()
    test_apply_config_template()
    test_delete_config_template()
    
    # 测试核心功能接口
    extract_task_id = test_extract_document(test_image)
    
    modifications = {"住院号": "TEST123", "病历号": "ABC456"}
    modify_task_id = test_modify_fields(test_image, modifications)
    
    if modify_task_id:
        download_file(modify_task_id, "modified.jpg", f"api_test_modified_{modify_task_id}.jpg")
    
    mosaic_fields = ["姓名", "病历号"]
    mosaic_task_id = test_apply_mosaic(test_image, mosaic_fields)
    
    if mosaic_task_id:
        download_file(mosaic_task_id, "mosaic.jpg", f"api_test_mosaic_{mosaic_task_id}.jpg")
    
    comprehensive_modifications = {"住院号": "COMP789"}
    comprehensive_mosaic_fields = ["姓名"]
    comprehensive_task_id = test_comprehensive_process(
        test_image, comprehensive_modifications, comprehensive_mosaic_fields
    )
    
    if comprehensive_task_id:
        download_file(comprehensive_task_id, "processed.jpg", f"api_test_comprehensive_{comprehensive_task_id}.jpg")
    
    # 清理临时文件
    print("\n清理临时文件...")
    for task_id in [extract_task_id, modify_task_id, mosaic_task_id, comprehensive_task_id]:
        if task_id:
            cleanup_task(task_id)
    
    print("\n所有API测试完成！")

if __name__ == "__main__":
    main()