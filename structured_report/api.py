#!/usr/bin/env python3
"""
结构化医学报告提取与隐私保护 - HTTP API 服务
提供文档提取、字段修改、马赛克处理等功能的Web API接口
"""

import multiprocessing
import os
import json
import uuid
from datetime import datetime
import sys

# 必须在导入任何其他模块之前设置多进程启动方法
try:
    multiprocessing.set_start_method('spawn', force=True)
except RuntimeError:
    pass

# 设置环境变量，减少资源泄漏
os.environ["OMP_NUM_THREADS"] = "1"
os.environ["MKL_NUM_THREADS"] = "1"
os.environ["OPENBLAS_NUM_THREADS"] = "1"
os.environ["NUMEXPR_NUM_THREADS"] = "1"
os.environ["PYTHONUNBUFFERED"] = "1"
os.environ["PADDLE_VERBOSE"] = "0"
os.environ["FLAGS_use_mkldnn"] = "0"
os.environ["FLAGS_cpu_deterministic"] = "1"
os.environ["CUDA_VISIBLE_DEVICES"] = "-1"  # 禁用CUDA
os.environ["PADDLE_TRAINERS_NUM"] = "1"
os.environ["PADDLE_TRAINER_ID"] = "0"

from flask import Flask, request, jsonify, send_file
from werkzeug.utils import secure_filename
import shutil

# 导入现有的功能模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import (
    extract_with_paddleocr_and_layoutlm,
    get_field_value_box,
    cleanup_ocr_instance
)
from text_modify_utils import modify_text_on_image
from extractor.mosaic_utils import mosaic_image_with_boxes

app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# 配置文件路径
CONFIG_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'extract_config.json')
CONFIG_BACKUP_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'config_backups')

# 配置文件上传
UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'temp_uploads')
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'pdf'}

# 确保目录存在
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(CONFIG_BACKUP_DIR, exist_ok=True)

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def generate_task_id():
    """生成唯一的任务ID"""
    return str(uuid.uuid4())

def cleanup_temp_files(task_id):
    """清理临时文件"""
    temp_dir = os.path.join(UPLOAD_FOLDER, task_id)
    if os.path.exists(temp_dir):
        shutil.rmtree(temp_dir)



def save_config(config_data, append_mode=False):
    """保存配置文件
    
    Args:
        config_data: 要保存的配置数据
        append_mode: 是否为追加模式，True表示追加，False表示覆盖
    """
    try:
        if append_mode:
            # 追加模式：读取现有配置，合并新配置
            existing_config = {}
            if os.path.exists(CONFIG_FILE):
                try:
                    with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                        existing_config = json.load(f)
                except Exception as e:
                    print(f"读取现有配置文件失败: {e}，将创建新配置")
                    existing_config = {"themes": []}
            
            # 合并配置
            if 'themes' not in existing_config:
                existing_config['themes'] = []
            
            # 追加新的主题
            if 'themes' in config_data:
                existing_config['themes'].extend(config_data['themes'])
            
            # 保存合并后的配置
            with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
                json.dump(existing_config, f, ensure_ascii=False, indent=2)
            
            return {"success": True, "message": "配置文件追加成功", "themes_added": len(config_data.get('themes', []))}
        else:
            # 覆盖模式：创建备份并保存新配置
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = os.path.join(CONFIG_BACKUP_DIR, f"extract_config_backup_{timestamp}.json")
            
            if os.path.exists(CONFIG_FILE):
                shutil.copy2(CONFIG_FILE, backup_file)
                print(f"配置文件已备份到: {backup_file}")
            
            # 保存新配置
            with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
            
            return {"success": True, "message": "配置文件更新成功", "backup": backup_file}
    except Exception as e:
        return {"error": f"配置文件保存失败: {str(e)}"}

def validate_config(config_data):
    """验证配置文件格式"""
    try:
        required_keys = ['themes']
        if not all(key in config_data for key in required_keys):
            return False, "配置文件缺少必要的 'themes' 字段"
        
        if not isinstance(config_data['themes'], list):
            return False, "'themes' 字段必须是数组"
        
        for theme in config_data['themes']:
            if not isinstance(theme, dict):
                return False, "每个主题必须是对象"
            
            if 'name' not in theme or 'fields' not in theme:
                return False, "每个主题必须包含 'name' 和 'fields' 字段"
            
            if not isinstance(theme['fields'], dict):
                return False, "'fields' 必须是对象"
            
            # id 字段是可选的，如果存在则必须是字符串
            if 'id' in theme and not isinstance(theme['id'], str):
                return False, "主题的 'id' 字段必须是字符串"
        
        return True, "配置文件格式正确"
    except Exception as e:
        return False, f"配置文件验证失败: {str(e)}"



def apply_mosaic_to_fields(image_path, field_names, ocr_results, output_path):
    """对指定字段应用马赛克处理"""
    try:
        # 收集需要马赛克的字段框
        mosaic_boxes = []
        for field_name in field_names:
            box = get_field_value_box(ocr_results, field_name)
            if box:
                mosaic_boxes.append(box)
                print(f"找到字段 '{field_name}' 的位置: {box}")
            else:
                print(f"警告: 未找到字段 '{field_name}' 的位置")
                # 尝试模糊匹配字段名
                for item in ocr_results:
                    if field_name in item.get('text', '') or item.get('text', '') in field_name:
                        if 'box' in item:
                            mosaic_boxes.append(item['box'])
                            print(f"通过模糊匹配找到字段 '{field_name}' 的位置: {item['box']}")
                            break
        
        if not mosaic_boxes:
            print("未找到任何需要马赛克的字段位置")
            # 返回False但创建空文件以避免500错误
            import shutil
            shutil.copy2(image_path, output_path)
            return True
        
        # 应用马赛克
        result_path = mosaic_image_with_boxes(image_path, mosaic_boxes, output_path)
        return result_path is not None
        
    except Exception as e:
        print(f"马赛克处理出错: {e}")
        # 出错时复制原文件
        import shutil
        shutil.copy2(image_path, output_path)
        return True

@app.route('/', methods=['GET'])
def index():
    """根路径接口 - API服务信息"""
    return jsonify({
        'service': '结构化医学报告提取与隐私保护 HTTP API',
        'version': 'v1.0.0',
        'status': 'running',
        'endpoints': {
            'index': 'GET / - API服务信息',
            'docs': 'GET /docs - 完整API文档',
            'health': 'GET /health - 健康检查',
            'extract': 'POST /extract - 文档结构化提取',
            'modify': 'POST /modify - 字段值修改',
            'mosaic': 'POST /mosaic - 马赛克处理',
            'process': 'POST /process - 综合处理',
            'config': 'GET/PUT /config - 配置文件管理（默认追加模式，可通过append_mode=false覆盖）',
            'config_backup': 'GET /config/backup - 获取配置备份列表',
            'config_restore': 'POST /config/restore - 恢复配置文件备份',
            'download': 'GET /download/<task_id>/<filename> - 文件下载',
            'cleanup': 'DELETE /cleanup/<task_id> - 清理临时文件'
        },
        'documentation': '详细API文档请查看项目README.md',
        'timestamp': datetime.now().isoformat()
    })

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({
        'status': 'healthy',
        'service': '结构化医学报告提取与隐私保护',
        'version': 'v1.0.0',
        'timestamp': datetime.now().isoformat()
    })

@app.route('/docs', methods=['GET'])
def api_docs():
    """API文档接口"""
    return jsonify({
        'title': '结构化医学报告提取与隐私保护 API 文档',
        'version': 'v1.0.0',
        'base_url': 'http://localhost:5000',
        'endpoints': [
            {
                'path': '/',
                'method': 'GET',
                'description': 'API服务信息',
                'parameters': None
            },
            {
                'path': '/health',
                'method': 'GET', 
                'description': '健康检查',
                'parameters': None
            },
            {
                'path': '/docs',
                'method': 'GET',
                'description': 'API文档',
                'parameters': None
            },
            {
                'path': '/extract',
                'method': 'POST',
                'description': '文档结构化提取',
                'parameters': {
                    'file': '上传的图片或PDF文件',
                    'config': '可选的配置文件内容（JSON格式）'
                }
            },
            {
                'path': '/modify',
                'method': 'POST',
                'description': '字段值修改',
                'parameters': {
                    'file': '上传的图片文件',
                    'modifications': '修改配置，格式为 {"字段名": "新值", ...}'
                }
            },
            {
                'path': '/mosaic',
                'method': 'POST',
                'description': '马赛克处理',
                'parameters': {
                    'file': '上传的图片文件',
                    'fields': '需要马赛克的字段列表，格式为 ["字段名1", "字段名2", ...]'
                }
            },
            {
                'path': '/process',
                'method': 'POST',
                'description': '综合处理（提取+修改+马赛克）',
                'parameters': {
                    'file': '上传的图片文件',
                    'modifications': '可选，修改配置',
                    'mosaic_fields': '可选，需要马赛克的字段列表'
                }
            },
            {
                'path': '/config',
                'method': 'GET',
                'description': '获取当前配置文件',
                'parameters': None
            },
            {
                'path': '/config',
                'method': 'PUT',
                'description': '更新配置文件',
                'parameters': {
                    'config': '新的配置文件内容（JSON格式）'
                }
            },
            {
                'path': '/config/backup',
                'method': 'GET',
                'description': '获取配置备份列表',
                'parameters': None
            },
            {
                'path': '/config/restore',
                'method': 'POST',
                'description': '恢复指定的配置文件备份',
                'parameters': {
                    'backup_file': '备份文件名'
                }
            },
            {
                'path': '/config/templates',
                'method': 'GET',
                'description': '获取配置模板列表',
                'parameters': None
            },
            {
                'path': '/config/template/save',
                'method': 'POST',
                'description': '保存配置模板',
                'parameters': {
                    'name': '模板名称',
                    'config': '配置数据',
                    'description': '模板描述（可选）'
                }
            },
            {
                'path': '/config/template/apply',
                'method': 'POST',
                'description': '应用配置模板',
                'parameters': {
                    'template_name': '模板名称'
                }
            },
            {
                'path': '/config/template/<name>',
                'method': 'DELETE',
                'description': '删除配置模板',
                'parameters': {
                    'name': '模板名称（URL参数）'
                }
            },
            {
                'path': '/download/<task_id>/<filename>',
                'method': 'GET',
                'description': '文件下载',
                'parameters': {
                    'task_id': '任务ID',
                    'filename': '文件名'
                }
            },
            {
                'path': '/cleanup/<task_id>',
                'method': 'DELETE',
                'description': '清理临时文件',
                'parameters': {
                    'task_id': '任务ID'
                }
            }
        ]
    })

@app.route('/config', methods=['GET'])
def get_config():
    """获取当前配置文件"""
    from extractor.config_loader import load_config
    config = load_config(CONFIG_FILE)
    if 'error' in config:
        return jsonify(config), 400
    return jsonify({
        'success': True,
        'config': config,
        'config_file': CONFIG_FILE,
        'timestamp': datetime.now().isoformat()
    })

@app.route('/config', methods=['PUT'])
def update_config():
    """更新配置文件（默认追加模式）"""
    try:
        if not request.is_json:
            return jsonify({'error': '请求必须是JSON格式'}), 400
        
        config_data = request.get_json()
        
        # 验证配置格式
        is_valid, message = validate_config(config_data)
        if not is_valid:
            return jsonify({'error': message}), 400
        
        # 检查是否指定了覆盖模式
        append_mode = config_data.pop('append_mode', True)  # 默认为追加模式
        
        # 提取主题中的 id 信息（用于返回）
        theme_ids = []
        if 'themes' in config_data:
            for theme in config_data['themes']:
                if 'id' in theme:
                    theme_ids.append({
                        'id': theme['id'],
                        'name': theme.get('name', 'Unknown')
                    })
        
        # 保存配置
        result = save_config(config_data, append_mode=append_mode)
        if 'error' in result:
            return jsonify(result), 500
        
        # 如果有主题 id，在返回结果中包含它们
        if theme_ids:
            result['theme_ids'] = theme_ids
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({'error': f'配置更新失败: {str(e)}'}), 500

@app.route('/config/backup', methods=['GET'])
def list_config_backups():
    """获取配置备份列表"""
    try:
        backup_files = []
        if os.path.exists(CONFIG_BACKUP_DIR):
            for file in os.listdir(CONFIG_BACKUP_DIR):
                if file.endswith('.json') and file.startswith('extract_config_backup_'):
                    file_path = os.path.join(CONFIG_BACKUP_DIR, file)
                    file_stat = os.stat(file_path)
                    backup_files.append({
                        'filename': file,
                        'size': file_stat.st_size,
                        'created_time': datetime.fromtimestamp(file_stat.st_ctime).isoformat(),
                        'modified_time': datetime.fromtimestamp(file_stat.st_mtime).isoformat()
                    })
        
        # 按创建时间排序，最新的在前
        backup_files.sort(key=lambda x: x['created_time'], reverse=True)
        
        return jsonify({
            'success': True,
            'backups': backup_files,
            'backup_dir': CONFIG_BACKUP_DIR,
            'total_count': len(backup_files)
        })
        
    except Exception as e:
        return jsonify({'error': f'获取备份列表失败: {str(e)}'}), 500

@app.route('/config/restore', methods=['POST'])
def restore_config():
    """恢复配置文件备份"""
    try:
        if not request.is_json:
            return jsonify({'error': '请求必须是JSON格式'}), 400
        
        data = request.get_json()
        backup_file = data.get('backup_file')
        
        if not backup_file:
            return jsonify({'error': '缺少 backup_file 参数'}), 400
        
        backup_path = os.path.join(CONFIG_BACKUP_DIR, backup_file)
        
        if not os.path.exists(backup_path):
            return jsonify({'error': f'备份文件不存在: {backup_file}'}), 404
        
        # 读取备份文件
        with open(backup_path, 'r', encoding='utf-8') as f:
            backup_config = json.load(f)
        
        # 验证备份配置格式
        is_valid, message = validate_config(backup_config)
        if not is_valid:
            return jsonify({'error': f'备份文件格式无效: {message}'}), 400
        
        # 恢复配置
        result = save_config(backup_config)
        if 'error' in result:
            return jsonify(result), 500
        
        return jsonify({
            'success': True,
            'message': f'配置文件已从备份恢复: {backup_file}',
            'restored_from': backup_file,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({'error': f'配置恢复失败: {str(e)}'}), 500



@app.route('/extract', methods=['POST'])
def extract_document():
    """
    文档结构化提取接口
    
    请求参数:
    - file: 上传的图片或PDF文件
    - config: 可选的配置文件内容（JSON格式）
    
    返回:
    - task_id: 任务ID
    - extracted_fields: 提取的结构化字段
    - ocr_text: OCR识别的原始文本
    """
    report_id = request.form.get('reportId') or (request.json and request.json.get('reportId'))
    if not report_id:
        return jsonify({'error': '缺少 reportId 参数'}), 400

    # 加载配置
    from extractor.config_loader import load_config
    config = load_config(CONFIG_FILE)
    # 根据 reportId 查找主题
    theme = None
    for t in config.get('themes', []):
        if str(t.get('id')) == str(report_id):
            theme = t
            break
    if not theme:
        return jsonify({'error': f'未找到 reportId={report_id} 的配置'}), 400
    
    try:
        # 检查文件是否存在
        if 'file' not in request.files:
            return jsonify({'error': '未找到上传文件'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': '未选择文件'}), 400
        
        if not allowed_file(file.filename):
            return jsonify({'error': '不支持的文件类型，请上传 PNG, JPG, JPEG 或 PDF 文件'}), 400
        
        # 生成任务ID和临时目录
        task_id = generate_task_id()
        task_dir = os.path.join(UPLOAD_FOLDER, task_id)
        os.makedirs(task_dir, exist_ok=True)
        
        # 保存上传的文件
        filename = secure_filename(file.filename)
        file_path = os.path.join(task_dir, filename)
        file.save(file_path)
        
        # 执行文档提取
        try:
            fields, ocr_results = extract_with_paddleocr_and_layoutlm(file_path)
            if not fields:
                fields = {}
            if not ocr_results:
                ocr_results = []
        except Exception as e:
            return jsonify({'error': f'文档提取失败: {str(e)}'}), 500
        
        # 加载配置并检测主题
        try:
            from extractor.config_loader import load_config
            from extractor.theme_detector import detect_theme
            
            config = load_config(CONFIG_FILE)
            ocr_text = '\n'.join([item.get('text', '') for item in ocr_results if isinstance(item, dict)])
            theme = detect_theme(ocr_text, config)
            
            if theme:
                # 使用主题配置提取字段
                theme_fields = theme.get('fields', {})
                extracted_fields = {}
                
                for field_name, field_config in theme_fields.items():
                    field_value = ""
                    
                    # 优先使用配置文件中的正则表达式
                    if field_config and 'regex' in field_config:
                        import re
                        match = re.search(field_config['regex'], ocr_text)
                        if match:
                            field_value = match.group(1).strip() if match.groups() else match.group(0).strip()
                    
                    # 如果正则没有匹配到，使用LayoutLM结果
                    if not field_value:
                        field_value = fields.get(field_name, "")
                    
                    extracted_fields[field_name] = field_value
                
                fields = extracted_fields
                theme_name = theme.get('name', 'UNKNOWN')
            else:
                theme_name = 'UNKNOWN'
                
        except Exception as e:
            print(f"主题检测失败: {e}")
            theme_name = 'UNKNOWN'
        
        # 构造完整的结果
        result = {
            'extracted_fields': fields,
            'ocr_results': ocr_results,
            'ocr_text': '\n'.join([item.get('text', '') for item in ocr_results if isinstance(item, dict)]),
            'theme': theme_name,
            'config_used': {
                'theme_name': theme_name,
                'field_count': len(fields),
                'extracted_count': len([v for v in fields.values() if v])
            }
        }
        
        # 保存结果到临时目录
        result_file = os.path.join(task_dir, 'result.json')
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        return jsonify({
            'task_id': task_id,
            'status': 'success',
            'extracted_fields': fields,
            'ocr_text': result['ocr_text'],
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({'error': f'处理文件时出错: {str(e)}'}), 500

@app.route('/modify', methods=['POST'])
def modify_fields():
    """
    字段值修改接口
    
    请求参数:
    - file: 上传的图片文件
    - modifications: 修改配置，格式为 {"字段名": "新值", ...}
    
    返回:
    - task_id: 任务ID
    - modified_image_url: 修改后图片的下载链接
    """
    try:
        # 检查文件是否存在
        if 'file' not in request.files:
            return jsonify({'error': '未找到上传文件'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': '未选择文件'}), 400
        
        if not allowed_file(file.filename):
            return jsonify({'error': '不支持的文件类型，请上传图片文件'}), 400
        
        # 获取修改配置
        modifications_str = request.form.get('modifications', '{}')
        try:
            modifications = json.loads(modifications_str)
        except json.JSONDecodeError:
            return jsonify({'error': '修改配置格式错误，请提供有效的JSON'}), 400
        
        if not modifications:
            return jsonify({'error': '未提供修改配置'}), 400
        
        # 生成任务ID和临时目录
        task_id = generate_task_id()
        task_dir = os.path.join(UPLOAD_FOLDER, task_id)
        os.makedirs(task_dir, exist_ok=True)
        
        # 保存上传的文件
        filename = secure_filename(file.filename)
        file_path = os.path.join(task_dir, filename)
        file.save(file_path)
        
        # 首先进行OCR识别获取字段位置
        try:
            fields, ocr_results = extract_with_paddleocr_and_layoutlm(file_path)
            if not fields:
                fields = {}
            if not ocr_results:
                ocr_results = []
        except Exception as e:
            return jsonify({'error': f'文档提取失败: {str(e)}'}), 500
        
        # 准备文本修改列表
        text_modifications = []
        for field_name, new_value in modifications.items():
            # 获取字段值的位置框
            box = get_field_value_box(ocr_results, field_name)
            if box:
                text_modifications.append((field_name, new_value, box))
            else:
                print(f"警告: 未找到字段 '{field_name}' 的位置")
        
        if not text_modifications:
            return jsonify({'error': '未找到可修改的字段位置'}), 400
        
        # 执行文本修改
        modified_image_path = modify_text_on_image(
            file_path, 
            text_modifications,
            os.path.join(task_dir, 'modified.jpg')
        )
        
        return jsonify({
            'task_id': task_id,
            'status': 'success',
            'modified_image_url': f'/download/{task_id}/modified.jpg',
            'modifications_applied': len(text_modifications),
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({'error': f'处理文件时出错: {str(e)}'}), 500

@app.route('/mosaic', methods=['POST'])
def apply_mosaic():
    """
    马赛克处理接口
    
    请求参数:
    - file: 上传的图片文件
    - fields: 需要马赛克的字段列表，格式为 ["字段名1", "字段名2", ...]
    
    返回:
    - task_id: 任务ID
    - mosaic_image_url: 马赛克处理后图片的下载链接
    """
    try:
        # 检查文件是否存在
        if 'file' not in request.files:
            return jsonify({'error': '未找到上传文件'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': '未选择文件'}), 400
        
        if not allowed_file(file.filename):
            return jsonify({'error': '不支持的文件类型，请上传图片文件'}), 400
        
        # 获取马赛克字段列表
        fields_str = request.form.get('fields', '[]')
        try:
            fields = json.loads(fields_str)
        except json.JSONDecodeError:
            return jsonify({'error': '字段列表格式错误，请提供有效的JSON数组'}), 400
        
        if not fields:
            return jsonify({'error': '未提供需要马赛克的字段'}), 400
        
        # 生成任务ID和临时目录
        task_id = generate_task_id()
        task_dir = os.path.join(UPLOAD_FOLDER, task_id)
        os.makedirs(task_dir, exist_ok=True)
        
        # 保存上传的文件
        filename = secure_filename(file.filename)
        file_path = os.path.join(task_dir, filename)
        file.save(file_path)
        
        # 首先进行OCR识别获取字段位置
        try:
            extracted_fields, ocr_results = extract_with_paddleocr_and_layoutlm(file_path)
            if not extracted_fields:
                extracted_fields = {}
            if not ocr_results:
                ocr_results = []
        except Exception as e:
            return jsonify({'error': f'文档提取失败: {str(e)}'}), 500
        
        # 执行马赛克处理
        mosaic_image_path = os.path.join(task_dir, 'mosaic.jpg')
        success = apply_mosaic_to_fields(
            file_path,
            fields,
            ocr_results,
            mosaic_image_path
        )
        
        if not success:
            return jsonify({'error': '马赛克处理失败'}), 500
        
        return jsonify({
            'task_id': task_id,
            'status': 'success',
            'mosaic_image_url': f'/download/{task_id}/mosaic.jpg',
            'fields_processed': len(fields),
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({'error': f'处理文件时出错: {str(e)}'}), 500

@app.route('/process', methods=['POST'])
def process_document():
    """
    综合处理接口（提取+修改+马赛克）
    
    请求参数:
    - file: 上传的图片文件
    - modifications: 可选，修改配置，格式为 {"字段名": "新值", ...}
    - mosaic_fields: 可选，需要马赛克的字段列表，格式为 ["字段名1", "字段名2", ...]
    
    返回:
    - task_id: 任务ID
    - extracted_fields: 提取的结构化字段
    - processed_image_url: 处理后图片的下载链接
    """
    try:
        # 检查文件是否存在
        if 'file' not in request.files:
            return jsonify({'error': '未找到上传文件'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': '未选择文件'}), 400
        
        if not allowed_file(file.filename):
            return jsonify({'error': '不支持的文件类型，请上传图片文件'}), 400
        
        # 获取处理配置
        modifications_str = request.form.get('modifications', '{}')
        mosaic_fields_str = request.form.get('mosaic_fields', '[]')
        
        try:
            modifications = json.loads(modifications_str)
            mosaic_fields = json.loads(mosaic_fields_str)
        except json.JSONDecodeError:
            return jsonify({'error': '配置格式错误，请提供有效的JSON'}), 400
        
        # 生成任务ID和临时目录
        task_id = generate_task_id()
        task_dir = os.path.join(UPLOAD_FOLDER, task_id)
        os.makedirs(task_dir, exist_ok=True)
        
        # 保存上传的文件
        filename = secure_filename(file.filename)
        file_path = os.path.join(task_dir, filename)
        file.save(file_path)
        
        # 执行OCR和结构化提取
        try:
            extracted_fields, ocr_results = extract_with_paddleocr_and_layoutlm(file_path)
            if not extracted_fields:
                extracted_fields = {}
            if not ocr_results:
                ocr_results = []
        except Exception as e:
            return jsonify({'error': f'文档提取失败: {str(e)}'}), 500
        
        current_image_path = file_path
        processed_steps = []
        
        # 如果有字段修改需求
        if modifications:
            text_modifications = []
            for field_name, new_value in modifications.items():
                box = get_field_value_box(ocr_results, field_name)
                if box:
                    text_modifications.append((field_name, new_value, box))
            
            if text_modifications:
                modified_image_path = os.path.join(task_dir, 'modified.jpg')
                current_image_path = modify_text_on_image(
                    current_image_path, 
                    text_modifications,
                    modified_image_path
                )
                processed_steps.append('field_modification')
        
        # 如果有马赛克需求
        if mosaic_fields:
            mosaic_image_path = os.path.join(task_dir, 'final.jpg')
            success = apply_mosaic_to_fields(
                current_image_path,
                mosaic_fields,
                ocr_results,
                mosaic_image_path
            )
            if success:
                current_image_path = mosaic_image_path
                processed_steps.append('mosaic')
        
        # 确定最终文件名
        final_filename = 'processed.jpg'
        if current_image_path != file_path:
            shutil.copy2(current_image_path, os.path.join(task_dir, final_filename))
        else:
            final_filename = filename
        
        return jsonify({
            'task_id': task_id,
            'status': 'success',
            'extracted_fields': extracted_fields,
            'ocr_text': '\n'.join([item['text'] for item in ocr_results]),
            'processed_image_url': f'/download/{task_id}/{final_filename}',
            'processing_steps': processed_steps,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({'error': f'处理文件时出错: {str(e)}'}), 500

@app.route('/download/<task_id>/<filename>', methods=['GET'])
def download_file(task_id, filename):
    """文件下载接口"""
    try:
        file_path = os.path.join(UPLOAD_FOLDER, task_id, filename)
        if not os.path.exists(file_path):
            return jsonify({'error': '文件不存在'}), 404
        
        # 检查文件是否可读
        if not os.access(file_path, os.R_OK):
            return jsonify({'error': '文件无法访问'}), 403
        
        # 获取文件大小
        file_size = os.path.getsize(file_path)
        if file_size == 0:
            return jsonify({'error': '文件为空'}), 400
        
        return send_file(
            file_path, 
            as_attachment=False, 
            download_name=filename,
            mimetype='image/jpeg' if filename.endswith('.jpg') else 'application/octet-stream'
        )
        
    except Exception as e:
        print(f"文件下载错误: {e}")
        return jsonify({'error': f'下载文件时出错: {str(e)}'}), 500

@app.route('/cleanup/<task_id>', methods=['DELETE'])
def cleanup_task(task_id):
    """清理任务临时文件"""
    try:
        cleanup_temp_files(task_id)
        return jsonify({
            'status': 'success',
            'message': f'任务 {task_id} 的临时文件已清理',
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({'error': f'清理文件时出错: {str(e)}'}), 500

@app.errorhandler(413)
def too_large(e):
    """文件过大错误处理"""
    return jsonify({'error': '文件过大，请上传小于16MB的文件'}), 413

@app.errorhandler(404)
def not_found(e):
    """404错误处理"""
    return jsonify({'error': '接口不存在'}), 404

@app.errorhandler(500)
def internal_error(e):
    """500错误处理"""
    return jsonify({'error': '服务器内部错误'}), 500

if __name__ == '__main__':
    import atexit
    # 注册程序退出时的清理函数
    atexit.register(cleanup_ocr_instance)
    
    print("启动结构化医学报告提取与隐私保护 HTTP API 服务")
    print("可用接口:")
    print("  GET  /health                 - 健康检查")
    print("  POST /extract                - 文档结构化提取")
    print("  POST /modify                 - 字段值修改")
    print("  POST /mosaic                 - 马赛克处理")
    print("  POST /process                - 综合处理")
    print("  GET  /download/<task_id>/<filename> - 文件下载")
    print("  DELETE /cleanup/<task_id>    - 清理临时文件")
    print()
    try:
        app.run(host='0.0.0.0', port=5001, debug=True)
    finally:
        # 确保资源清理
        cleanup_ocr_instance()