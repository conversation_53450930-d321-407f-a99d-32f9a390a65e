from PIL import Image

def expand_box(box, img_w, img_h, ratio=0.1, min_expand=5, right_offset=5, down_offset=5):
    """扩展边界框，添加偏移"""
    x0, y0, x1, y1 = [int(coord) for coord in box]  # 确保输入坐标是整数
    
    w = x1 - x0
    h = y1 - y0
    expand_w = max(int(w * ratio), min_expand)
    expand_h = max(int(h * ratio), min_expand)
    
    x0 = x0  # 不向左扩展
    y0 = max(0, y0 - expand_h + down_offset)
    x1 = min(img_w, x1 + expand_w + right_offset)
    y1 = min(img_h, y1 + expand_h + down_offset)
    
    return [int(x0), int(y0), int(x1), int(y1)]  # 确保返回整数坐标

def mosaic_area(img, box, mosaic_size=10):
    """对图片的指定区域进行马赛克处理"""
    x0, y0, x1, y1 = [int(coord) for coord in box]  # 确保所有坐标都是整数
    
    # 确保坐标在图片范围内
    width, height = img.size
    x0 = max(0, min(x0, width))
    y0 = max(0, min(y0, height))
    x1 = max(0, min(x1, width))
    y1 = max(0, min(y1, height))
    
    # 确保区域有效
    if x0 >= x1 or y0 >= y1:
        return
    
    # 获取指定区域
    region = img.crop((x0, y0, x1, y1))
    
    # 缩小到很小的尺寸
    small = region.resize(
        (max(1, int((x1 - x0) / mosaic_size)), max(1, int((y1 - y0) / mosaic_size))),
        Image.NEAREST
    )
    
    # 放大回原尺寸
    mosaic = small.resize((x1 - x0, y1 - y0), Image.NEAREST)
    
    # 将马赛克区域粘贴回原图
    img.paste(mosaic, (x0, y0))

def mosaic_image_with_boxes(image_path, boxes, output_path=None, mosaic_size=20):
    """对图片的多个区域进行马赛克处理"""
    img = Image.open(image_path)
    img_w, img_h = img.size
    
    # 使用原始框，不进行任何扩展
    expanded_boxes = []
    for box in boxes:
        # 确保坐标是整数，但不扩展
        expanded_box = [int(coord) for coord in box]
        expanded_boxes.append(expanded_box)
    
    # 对每个区域进行马赛克处理
    for box in expanded_boxes:
        mosaic_area(img, box, mosaic_size)
    
    # 保存结果
    if output_path is None:
        output_path = image_path.replace('.jpg', '_mosaic.jpg').replace('.png', '_mosaic.png')
    
    # 确保图片是RGB模式
    if img.mode == 'RGBA':
        img = img.convert('RGB')
    
    img.save(output_path)
    print(f'已保存打码图片: {output_path}')
    return output_path 