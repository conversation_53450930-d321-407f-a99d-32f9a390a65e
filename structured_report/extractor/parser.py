import re

def fuzzy_search(patterns, text):
    for pat in patterns:
        m = re.search(pat, text, re.MULTILINE)
        if m:
            return m
    return None

def extract_indicators(text):
    """
    从医学报告文本中抽取结构化主要信息和指标项，提升容错能力，支持医学部位字典映射。
    """
    indicators = {}
    # 医学部位映射表（常见错别字、拼音、英文等）
    region_map = {
        '顶叶': ['顶叶', 'dingye', 'ding', '顶', '项叶', '项', '顶页', '项页', '顶业', '项业', 'Bb', '顶 叶', '项 叶'],
        '枕叶': ['枕叶', 'zhenye', '枕', '枕页', '枕业', 'Bb', '枕 叶'],
        '颞叶': ['颞叶', 'nieye', '颞', '颞页', '颞业', '颞 叶'],
        '扣带回': ['扣带回', 'koudaohui', '扣带', '带回', '扣 带 回'],
        '额叶': ['额叶', 'eye', '额', '额页', '额业', '额 叶'],
        '半卵圆区': ['半卵圆区', '卵圆区', '卵圆', '半卵圆', '半 卵圆区'],
        '脑室旁': ['脑室旁', '脑室', '脑旁', '脑 室旁'],
    }
    def map_region(raw):
        for k, vlist in region_map.items():
            for v in vlist:
                if v in raw:
                    return k
        return raw
    # 病案号容错
    case_number = fuzzy_search([
        r'[病痛][历案]?[号码][\s:：;；\^\-]*([A-Za-z0-9]+)',
        r'号[\s:：;；\^\-]*([A-Za-z0-9]+)',
        r'ba[\s:：;；\^\-]*([A-Za-z0-9]+)',
        r'B[\s:：;；\^\-]*([A-Za-z0-9]+)'
    ], text)
    if case_number:
        indicators['病案号'] = case_number.group(1)
    # 送检医院容错
    hospital = fuzzy_search([
        r'(送检医院|送检医院:|送检医院：|迭橙医院:|解放路院区|邵逸夫医院|附属.*?医院|医学院附属第二医院|浙江大学医学院附属第二医院|医院)',
    ], text)
    if hospital:
        indicators['送检医院'] = hospital.group(1)
    # 送检科室容错
    department = fuzzy_search([
        r'(送检科室|送检科室:|送检科室：|透蓝禅室:|神经内科门诊[\-\w]*|神经内科|内科门诊|放射科|核医学科|神经内科门诊[\-\w]*|神经内科门诊|神经内科 门诊)',
    ], text)
    if department:
        indicators['送检科室'] = department.group(1)
    # 临床诊断容错与多行提取
    diagnosis = fuzzy_search([
        r'临[\s]*床[\s]*诊[断沈][\s:：;；\^\-]*([\u4e00-\u9fa5A-Za-z（）()\s\-]+?)(?:\n|$|影像|检[查测]|PET|CT)',
        r'诊[断沈][\s:：;；\^\-]*([\u4e00-\u9fa5A-Za-z（）()\s\-]+?)(?:\n|$|影像|检[查测]|PET|CT)'
    ], text)
    if diagnosis:
        indicators['临床诊断'] = diagnosis.group(1).strip()
    # 检查项目容错
    exam_item = fuzzy_search([
        r'检查项目[\s:：;；\^\-]*([\u4e00-\u9fa5A-Za-z0-9\-（）()]+)',
        r'(脑 PIB 代谢显像 \(AD\))',
        r'(PIB 代谢显像)'
    ], text)
    if exam_item:
        indicators['检查项目'] = exam_item.group(1)
    # 显像剂容错
    tracer = fuzzy_search([
        r'显[像像][剂剂][\s:：;；\^\-]*([A-Za-z0-9\-/]+)',
        r'18F[\- ]?AV45',
        r'(AV45|AV 45|18F-AV45|I8F-AV45|I8F|18F)',
    ], text)
    if tracer:
        indicators['显像剂'] = tracer.group(1).replace('I8F', '18F').replace('AV 45', 'AV45') if tracer.lastindex else '18F-AV45'
    # 剂量容错
    dose = fuzzy_search([
        r'剂量[\s:：;；\^\-]*([0-9.]+\s*mCi)',
        r'剂量[\s:：;；\^\-]*([0-9.]+mCi)',
        r'剂[ ^量:：;；\^\-]*([0-9.]+mCi)',
        r'(11mCi|IImCI|llmCi|llmcl|llmcl)',
    ], text)
    if dose:
        indicators['剂量'] = dose.group(1).replace('IImCI', '11mCi').replace('llmCi', '11mCi').replace('llmcl', '11mCi').replace(' ', '')
    # 检查日期容错
    exam_date = fuzzy_search([
        r'检[查耷][日期期][\s:：;；\^\-]*([0-9]{4}[\/-][0-9]{1,2}[\/-][0-9]{1,2})',
        r'([0-9]{4}[\/-][0-9]{1,2}[\/-][0-9]{1,2})'
    ], text)
    if exam_date:
        indicators['检查日期'] = exam_date.group(1)
    # 指标项容错（部位+SUVmean=数值，允许错别字和常见变体）
    indicator_list = []
    for m in re.finditer(r'([\u4e00-\u9fa5A-Za-z]+)[\s\(（]*SUVm[eaon]{2,4}[=：<>-]{0,2}\s*([0-9lL\. ]+)', text):
        region = map_region(m.group(1))
        value = m.group(2).replace('l', '1').replace('L', '1').replace(' ', '').replace('..', '.').strip('.').strip()
        indicator_list.append({"部位": region, "SUVmean": value})
    if indicator_list:
        indicators['指标项'] = indicator_list
    # 修正单独的SUVmean_字段
    for k in list(indicators.keys()):
        if k.startswith('SUVmean_'):
            indicators[k] = indicators[k].replace('l', '1').replace('L', '1').replace(' ', '').replace('..', '.').strip('.').strip()
    # 检查所见容错
    check_result = re.search(r'(?:检查所见|/查见|查见)[\s:：;；\^\-]*([\s\S]+?)(?:影像诊断|诊断结果|$)', text)
    if check_result:
        indicators['检查所见'] = check_result.group(1).strip()
    # 诊断结果提取
    diagnosis_result = re.search(r'(?:诊断结果|断筅果)[:：]([\s\S]+?)(?:\n|$)', text)
    if diagnosis_result:
        indicators['诊断结果'] = diagnosis_result.group(1).strip()
    # 报告日期提取（增强容错，支持各种分隔符和错别字，并格式化为yyyy/m/d）
    report_date = re.search(r'(?:报告日期|报丐日期)[;:：;；]?\s*([0-9O?]{4}[\/\-]?[0-9]{1,4}[\/\-]?[0-9]{1,2})', text)
    if report_date:
        date_raw = report_date.group(1).replace('O', '0').replace('?', '2')
        import re as _re
        date_parts = _re.findall(r'\d+', date_raw)
        if len(date_parts) == 2 and len(date_parts[1]) == 3:
            # 2025/422 这种情况
            year = date_parts[0]
            month = int(date_parts[1][0])
            day = int(date_parts[1][1:])
            indicators['报告日期'] = f"{year}/{month}/{day}"
        elif len(date_parts) == 3:
            indicators['报告日期'] = f"{date_parts[0]}/{int(date_parts[1])}/{int(date_parts[2])}"
        else:
            indicators['报告日期'] = date_raw
    # PET中心地址提取（支持“PET 中  地址:”等变体，并修正常见错别字）
    pet_address = re.search(r'PET[\s中]*中心?[\s]*地址[:：]?[\s]*([^\n]+)', text)
    if not pet_address:
        pet_address = re.search(r'PET[\s中]*[\s]*地址[:：]?[\s]*([^\n]+)', text)
    if pet_address:
        addr = pet_address.group(1).strip()
        # OCR错别字修正
        addr = addr.replace('上溽币', '上海市').replace('吴中东路', '吴中路')
        indicators['PET中心地址'] = addr
    # 电话提取（支持“电话”、“电诂”等错别字）
    phone = re.search(r'电[话诂][:：]?\s*([0-9\-]+)', text)
    if phone:
        indicators['电话'] = phone.group(1).strip()
    # 影像诊断（多种字段名容错，终止条件更宽松，分割用splitlines）
    m = re.search(r'影[像I1l]?诊断[:：]([\s\S]+?)(?:\(|（|\[|\{|\)|$)', text)
    if m:
        lines = []
        for line in m.group(1).splitlines():
            line = line.strip()
            if not line or line.startswith('(') or line.startswith('（') or '报告仅供' in line or '搌告仅供' in line:
                break
            # 去除行首序号
            line = re.sub(r'^[0-9]+[.、]\s*', '', line)
            lines.append(line)
        indicators['影像诊断'] = lines
    else:
        indicators['影像诊断'] = []
    return indicators 