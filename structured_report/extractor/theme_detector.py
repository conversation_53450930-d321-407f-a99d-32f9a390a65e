def detect_theme(text, config):
    """
    根据文本内容检测最匹配的主题
    :param text: OCR识别的文本内容
    :param config: 配置文件
    :return: 匹配的主题配置，如果没有匹配则返回第一个主题
    """
    if not config or 'themes' not in config or not config['themes']:
        return None
    
    # 如果没有主题配置，返回None
    if len(config['themes']) == 0:
        return None
    
    # 如果有多个主题，尝试根据文本内容智能选择
    if len(config['themes']) > 1:
        # 简单的主题选择逻辑：根据主题名称在文本中的出现情况
        best_theme = None
        best_score = 0
        
        for theme in config['themes']:
            theme_name = theme.get('name', '')
            if not theme_name:
                continue
                
            # 计算主题名称在文本中的匹配分数
            # 完全匹配得分最高，部分匹配次之
            if theme_name in text:
                score = len(theme_name) * 2  # 完全匹配
            elif any(word in text for word in theme_name.split()):
                score = len(theme_name)  # 部分匹配
            else:
                score = 0
            
            if score > best_score:
                best_score = score
                best_theme = theme
        
        # 如果找到了匹配的主题，返回它
        if best_theme and best_score > 0:
            return best_theme
    
    # 如果没有找到匹配的主题，或者只有一个主题，返回第一个主题
    return config['themes'][0] 