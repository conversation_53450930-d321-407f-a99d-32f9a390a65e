import os
import json
from . import parser

def extract_text_from_image(image_path):
    # 只用 EasyOCR，PaddleOCR在ARM Mac下不稳定
    try:
        import easyocr
        reader = easyocr.Reader(['ch_sim', 'en'])
        result = reader.readtext(image_path, detail=0)
        text = '\n'.join(result)
        return text
    except Exception as e:
        print(f'EasyOCR不可用，原因：{e}\n尝试pytesseract...')
    # pytesseract分支
    try:
        import pytesseract
        from PIL import Image
        text = pytesseract.image_to_string(Image.open(image_path), lang='chi_sim+eng')
        return text
    except Exception as e:
        print(f'pytesseract不可用，原因：{e}')
        return ''

def ocr_and_extract_fields(img_path):
    text = extract_text_from_image(img_path)
    indicators = parser.extract_indicators(text)
    return text, indicators

# =============================
# PaddleOCR 结果转 LayoutLM 输入
# =============================
def normalize_box(box, width, height):
    """
    兼容 box 为 [[x1, y1], ...] 或 [x0, y0, x1, y1] 两种格式
    返回 [x0, y0, x1, y1] 格式，归一化到 0~1000
    """
    if isinstance(box[0], (list, tuple)):  # 4点坐标
        x_coords = [point[0] for point in box]
        y_coords = [point[1] for point in box]
        x0, y0 = min(x_coords), min(y_coords)
        x1, y1 = max(x_coords), max(y_coords)
    else:  # 4个数
        x0, y0, x1, y1 = box
    return [
        int(1000 * x0 / width),
        int(1000 * y0 / height),
        int(1000 * x1 / width),
        int(1000 * y1 / height)
    ]

def ocr_results_to_layoutlm_inputs(ocr_results, img_w, img_h, split_by_char=True):
    """
    将 PaddleOCR 结果转为 LayoutLM 输入格式
    :param ocr_results: [{'text': '姓名', 'box': [[x1, y1], ...]}, ...]
    :param img_w: 图片宽度
    :param img_h: 图片高度
    :param split_by_char: 是否按字分割（中文推荐True，英文可设为False按空格分词）
    :return: words, boxes
    """
    words = []
    boxes = []
    for item in ocr_results:
        text = item['text']
        box = item['box']
        if split_by_char:
            tokens = list(text)
        else:
            tokens = text.split()
        for token in tokens:
            words.append(token)
            boxes.append(normalize_box(box, img_w, img_h))
    return words, boxes

# 用法示例：
# ocr_results = [
#     {"text": "姓名", "box": [[10, 20], [60, 20], [60, 40], [10, 40]]},
#     {"text": "张三", "box": [[70, 20], [120, 20], [120, 40], [70, 40]]}
# ]
# img_w, img_h = 200, 100
# words, boxes = ocr_results_to_layoutlm_inputs(ocr_results, img_w, img_h)
# print(words)  # ['姓', '名', '张', '三']
# print(boxes)  # [[50, 200, 300, 400], ...] 归一化后的 box 