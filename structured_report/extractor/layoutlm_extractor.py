import torch
from transformers import LayoutLMTokenizer, LayoutLMForTokenClassification
import re

# 使用本地模型路径
MODEL_PATH = "./models/layoutlm-base-uncased"  # 本地模型目录

# 自动选择 MPS (Apple Silicon) 或 CPU
device = torch.device('mps' if torch.backends.mps.is_available() else 'cpu')

tokenizer = LayoutLMTokenizer.from_pretrained(MODEL_PATH)
model = LayoutLMForTokenClassification.from_pretrained(MODEL_PATH).to(device)
model.eval()

# 标签映射（
LABEL_MAP = {
    0: 'O',
    1: 'B-姓名', 2: 'I-姓名',
    3: 'B-检查项目', 4: 'I-检查项目',
    5: 'B-影像诊断', 6: 'I-影像诊断',
}

def extract_field_by_regex(text, regex):
    match = re.search(regex, text)
    if match:
        return match.group(1).strip()
    return ""

def extract_fields_with_layoutlm(words, boxes, max_length=512):
    """
    用 LayoutLM 对 OCR结果进行结构化字段抽取
    :param words: 文本token列表
    :param boxes: 对应的box列表
    :param max_length: 最大长度
    :return: 字段字典，如 {'姓名': '张三', '检查项目': 'PET', ...}
    """
    encoding = tokenizer(
        words,
        boxes=boxes,
        return_tensors="pt",
        padding="max_length",
        truncation=True,
        max_length=max_length,
        is_split_into_words=True
    )
    # 转到 device
    encoding = {k: v.to(device) for k, v in encoding.items()}
    with torch.no_grad():
        outputs = model(**encoding)
        logits = outputs.logits
        predictions = torch.argmax(logits, dim=2)[0].tolist()
    # 还原字段
    fields = {}
    current_field = None
    current_value = []
    for word, pred in zip(words, predictions):
        label = LABEL_MAP.get(pred, 'O')
        if label.startswith('B-'):
            if current_field and current_value:
                fields[current_field] = ''.join(current_value)
            current_field = label[2:]
            current_value = [word]
        elif label.startswith('I-') and current_field == label[2:]:
            current_value.append(word)
        else:
            if current_field and current_value:
                fields[current_field] = ''.join(current_value)
            current_field = None
            current_value = []
    if current_field and current_value:
        fields[current_field] = ''.join(current_value)
    return fields

