import torch
from transformers import LayoutLMTokenizer, LayoutLMForTokenClassification
import re
import os

# 使用绝对路径确保模型能正确加载
def get_model_path():
    """获取模型的绝对路径"""
    # 获取当前文件的目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    # 向上两级目录到项目根目录，然后进入models目录
    project_root = os.path.dirname(os.path.dirname(current_dir))
    model_path = os.path.join(project_root, "models", "layoutlm-base-uncased")
    return model_path

MODEL_PATH = get_model_path()

# 自动选择 MPS (Apple Silicon) 或 CPU
device = torch.device('mps' if torch.backends.mps.is_available() else 'cpu')

# 延迟加载模型，避免导入时出错
tokenizer = None
model = None

def load_model():
    """延迟加载模型"""
    global tokenizer, model
    if tokenizer is None or model is None:
        try:
            print(f"🔍 正在加载LayoutLM模型，路径: {MODEL_PATH}")
            if not os.path.exists(MODEL_PATH):
                raise FileNotFoundError(f"模型路径不存在: {MODEL_PATH}")

            tokenizer = LayoutLMTokenizer.from_pretrained(MODEL_PATH)
            model = LayoutLMForTokenClassification.from_pretrained(MODEL_PATH).to(device)
            model.eval()
            print(f"✅ LayoutLM模型加载成功，设备: {device}")
        except Exception as e:
            print(f"❌ LayoutLM模型加载失败: {e}")
            print(f"模型路径: {MODEL_PATH}")
            print(f"路径是否存在: {os.path.exists(MODEL_PATH)}")
            if os.path.exists(MODEL_PATH):
                print(f"模型目录内容: {os.listdir(MODEL_PATH)}")
            raise e

# 标签映射（
LABEL_MAP = {
    0: 'O',
    1: 'B-姓名', 2: 'I-姓名',
    3: 'B-检查项目', 4: 'I-检查项目',
    5: 'B-影像诊断', 6: 'I-影像诊断',
}

def extract_field_by_regex(text, regex):
    match = re.search(regex, text)
    if match:
        return match.group(1).strip()
    return ""

def extract_fields_with_layoutlm(words, boxes, max_length=512):
    """
    用 LayoutLM 对 OCR结果进行结构化字段抽取
    :param words: 文本token列表
    :param boxes: 对应的box列表
    :param max_length: 最大长度
    :return: 字段字典，如 {'姓名': '张三', '检查项目': 'PET', ...}
    """
    # 确保模型已加载
    load_model()

    # 如果没有输入数据，返回空字典
    if not words or not boxes:
        return {}

    try:
        encoding = tokenizer(
            words,
            boxes=boxes,
            return_tensors="pt",
            padding="max_length",
            truncation=True,
            max_length=max_length,
            is_split_into_words=True
        )
        # 转到 device
        encoding = {k: v.to(device) for k, v in encoding.items()}
        with torch.no_grad():
            outputs = model(**encoding)
            logits = outputs.logits
            predictions = torch.argmax(logits, dim=2)[0].tolist()

        # 还原字段
        fields = {}
        current_field = None
        current_value = []
        for word, pred in zip(words, predictions):
            label = LABEL_MAP.get(pred, 'O')
            if label.startswith('B-'):
                if current_field and current_value:
                    fields[current_field] = ''.join(current_value)
                current_field = label[2:]
                current_value = [word]
            elif label.startswith('I-') and current_field == label[2:]:
                current_value.append(word)
            else:
                if current_field and current_value:
                    fields[current_field] = ''.join(current_value)
                current_field = None
                current_value = []
        if current_field and current_value:
            fields[current_field] = ''.join(current_value)
        return fields
    except Exception as e:
        print(f"❌ LayoutLM字段提取失败: {e}")
        return {}

