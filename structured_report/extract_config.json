{"themes": [{"name": "测试1", "id": "9", "fields": {"姓名": {"regex": "姓名[:：]?\\s*([\\u4e00-\\u9fa5·\\.]{1,10})", "description": "name"}, "病历号": {"regex": "病历号[:：]?\\s*([0-9A-Za-z]+)", "description": "number"}, "性别": {"regex": "性别[:：]?\\s*([男女])", "description": "gender"}}}, {"name": "测试2", "id": "10", "fields": {"姓名": {"regex": "姓名[:：]?\\\\s*([\\\\u4e00-\\\\u9fa5·\\\\.]{1,10})", "description": "name"}, "病历号": {"regex": "病历号[:：]?\\\\s*([0-9A-Za-z]+)", "description": "number"}}}]}