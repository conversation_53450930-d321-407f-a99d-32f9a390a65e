#!/usr/bin/env python3
"""
文本修改工具模块
用于在图片上替换指定字段的文本内容
"""

from PIL import Image, ImageDraw, ImageFont
import os

def modify_text_on_image(image_path, text_modifications, output_path=None):
    """
    在图片上修改指定字段的文本内容
    
    Args:
        image_path (str): 输入图片路径
        text_modifications (list): 文本修改列表，每个元素为 (field_name, new_text, box)
        output_path (str): 输出图片路径，如果为None则自动生成
    
    Returns:
        str: 输出图片路径
    """
    # 打开图片
    img = Image.open(image_path)
    # 如果是RGBA模式，转换为RGB模式
    if img.mode == 'RGBA':
        img = img.convert('RGB')
    draw = ImageDraw.Draw(img)
    
    # 尝试加载字体，如果失败则使用默认字体
    font = get_best_font()
    
    # 处理每个文本修改
    for field_name, new_text, box in text_modifications:
        if box is None:
            print(f"警告: 字段 '{field_name}' 的box为空，跳过修改")
            continue
            
        x0, y0, x1, y1 = box
        
        # 计算文本区域
        text_width = x1 - x0
        text_height = y1 - y0
        
        # 获取文本的边界框
        try:
            bbox = draw.textbbox((0, 0), new_text, font=font)
            text_bbox_width = bbox[2] - bbox[0]
            text_bbox_height = bbox[3] - bbox[1]
        except:
            # 如果无法获取文本边界框，使用估算值
            text_bbox_width = len(new_text) * 12  # 估算每个字符12像素
            text_bbox_height = 16
        
        # 计算文本位置（居中）
        text_x = x0 + (text_width - text_bbox_width) // 2
        text_y = y0 + (text_height - text_bbox_height) // 2
        
        # 确保文本位置不超出边界
        text_x = max(x0, text_x)
        text_y = max(y0, text_y)
        
        # 先用白色填充原文本区域
        draw.rectangle([x0, y0, x1, y1], fill='white')
        
        # 绘制新文本
        try:
            draw.text((text_x, text_y), new_text, fill='black', font=font)
            print(f"已修改字段 '{field_name}' 的文本为: '{new_text}'")
        except Exception as e:
            print(f"修改字段 '{field_name}' 时出错: {e}")
    
    # 保存修改后的图片
    if output_path is None:
        if image_path.lower().endswith('.jpg'):
            output_path = image_path[:-4] + '_modified.jpg'
        else:
            output_path = image_path + '_modified.jpg'
    
    img.save(output_path)
    print(f'已保存修改后的图片: {output_path}')
    
    return output_path

def get_best_font(font_size=16):
    """
    获取最佳可用字体
    
    Args:
        font_size (int): 字体大小
    
    Returns:
        ImageFont: 最佳可用字体
    """
    # 尝试使用系统中文字体
    font_paths = [
        '/System/Library/Fonts/PingFang.ttc',  # macOS
        '/System/Library/Fonts/STHeiti Light.ttc',  # macOS
        '/System/Library/Fonts/Arial Unicode MS.ttf',  # macOS
        '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',  # Linux
        '/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf',  # Linux
        'C:/Windows/Fonts/simhei.ttf',  # Windows
        'C:/Windows/Fonts/msyh.ttc',  # Windows 微软雅黑
        'C:/Windows/Fonts/simsun.ttc',  # Windows 宋体
    ]
    
    for font_path in font_paths:
        if os.path.exists(font_path):
            try:
                font = ImageFont.truetype(font_path, font_size)
                print(f"使用字体: {font_path}")
                return font
            except Exception as e:
                print(f"字体加载失败 {font_path}: {e}")
                continue
    
    # 如果所有字体都失败，使用默认字体
    print("使用默认字体")
    return ImageFont.load_default()

def modify_and_mosaic_image(image_path, text_modifications, mosaic_boxes, output_path=None):
    """
    同时进行文本修改和马赛克处理
    
    Args:
        image_path (str): 输入图片路径
        text_modifications (list): 文本修改列表
        mosaic_boxes (list): 马赛克框列表
        output_path (str): 输出图片路径
    
    Returns:
        str: 输出图片路径
    """
    # 先进行文本修改
    temp_path = image_path[:-4] + '_temp.jpg' if image_path.lower().endswith('.jpg') else image_path + '_temp'
    
    if text_modifications:
        modify_text_on_image(image_path, text_modifications, temp_path)
        current_image_path = temp_path
    else:
        current_image_path = image_path
    
    # 再进行马赛克处理
    if mosaic_boxes:
        from extractor.mosaic_utils import mosaic_image_with_boxes
        if output_path is None:
            if image_path.lower().endswith('.jpg'):
                output_path = image_path[:-4] + '_modified_mosaic.jpg'
            else:
                output_path = image_path + '_modified_mosaic.jpg'
        
        mosaic_image_with_boxes(current_image_path, mosaic_boxes, output_path=output_path)
        
        # 清理临时文件
        if text_modifications and os.path.exists(temp_path):
            os.remove(temp_path)
    else:
        # 如果没有马赛克，直接使用修改后的图片
        if output_path is None:
            output_path = current_image_path
        elif current_image_path != output_path:
            import shutil
            shutil.copy2(current_image_path, output_path)
    
    return output_path 