import argparse
import atexit
import multiprocessing
import os
import json
import re

# 必须在导入任何其他模块之前设置多进程启动方法
try:
    multiprocessing.set_start_method('spawn', force=True)
except RuntimeError:
    pass

# 设置环境变量，减少资源泄漏
os.environ["OMP_NUM_THREADS"] = "1"
os.environ["MKL_NUM_THREADS"] = "1"
os.environ["OPENBLAS_NUM_THREADS"] = "1"
os.environ["NUMEXPR_NUM_THREADS"] = "1"
os.environ["PYTHONUNBUFFERED"] = "1"
os.environ["PADDLE_VERBOSE"] = "0"
# 禁用PaddleOCR的多进程和GPU
os.environ["FLAGS_use_mkldnn"] = "0"
os.environ["FLAGS_cpu_deterministic"] = "1"
os.environ["CUDA_VISIBLE_DEVICES"] = "-1"  # 禁用CUDA
# 控制多进程
os.environ["PADDLE_TRAINERS_NUM"] = "1"
os.environ["PADDLE_TRAINER_ID"] = "0"

from extractor.config_loader import load_config
from extractor.theme_detector import detect_theme
from extractor import parser
from extractor.mosaic_utils import mosaic_image_with_boxes
from text_modify_utils import modify_and_mosaic_image

from PIL import Image
from paddleocr import PaddleOCR
from extractor.ocr_utils import ocr_results_to_layoutlm_inputs
from extractor.layoutlm_extractor import extract_fields_with_layoutlm

CONFIG_PATH = os.path.join(os.path.dirname(__file__), 'extract_config.json')

# 全局 OCR 实例变量
_ocr_instance = None

def get_ocr_instance():
    """获取OCR实例，使用懒加载模式"""
    global _ocr_instance
    if _ocr_instance is None:
        try:
            print("🔍 正在初始化PaddleOCR...")
            # 使用基本配置，通过环境变量控制多进程行为
            _ocr_instance = PaddleOCR(
                lang='ch',
                use_angle_cls=False,
                det_limit_side_len=960,
                show_log=False
            )
            print("✅ PaddleOCR初始化成功")
        except Exception as e:
            print(f"❌ PaddleOCR初始化失败: {e}")
            print("💡 提示: 请检查网络连接或使用离线模式")
            raise e
    return _ocr_instance

def cleanup_ocr_instance():
    """清理OCR实例资源"""
    global _ocr_instance
    if _ocr_instance is not None:
        try:
            # 尝试清理PaddleOCR的资源
            if hasattr(_ocr_instance, 'text_detector') and _ocr_instance.text_detector:
                del _ocr_instance.text_detector
            if hasattr(_ocr_instance, 'text_recognizer') and _ocr_instance.text_recognizer:
                del _ocr_instance.text_recognizer
            if hasattr(_ocr_instance, 'text_classifier') and _ocr_instance.text_classifier:
                del _ocr_instance.text_classifier
            if hasattr(_ocr_instance, 'use_angle_cls'):
                _ocr_instance.use_angle_cls = False
            del _ocr_instance
        except Exception as e:
            print(f"清理OCR实例时出现错误: {e}")
        finally:
            _ocr_instance = None
            
        # 强制垃圾回收
        import gc
        gc.collect()
        
        # 清理多进程相关资源
        try:
            import multiprocessing
            # 清理活跃的进程
            for p in multiprocessing.active_children():
                if p.is_alive():
                    p.terminate()
                    p.join(timeout=1)
        except Exception as e:
            print(f"清理多进程资源时出现错误: {e}")

# 注册程序退出时的清理函数
atexit.register(cleanup_ocr_instance)

def extract_with_paddleocr_and_layoutlm(image_path):
    # 使用懒加载的 OCR 实例
    try:
        ocr_instance = get_ocr_instance()
        result = ocr_instance.ocr(image_path)
        # 适配新版 PaddleOCR 结果结构
        if isinstance(result, list) and len(result) > 0:
            if isinstance(result[0], dict) and 'rec_texts' in result[0]:
                # 新版格式
                rec_texts = result[0]['rec_texts']
                rec_boxes = result[0]['rec_boxes']
                ocr_results = []
                for text, box in zip(rec_texts, rec_boxes):
                    ocr_results.append({'text': text, 'box': box.tolist() if hasattr(box, 'tolist') else box})
            else:
                # 旧版格式
                ocr_results = []
                for line in result[0]:
                    text = line[1][0]
                    box = line[0]
                    ocr_results.append({'text': text, 'box': box})
        else:
            ocr_results = []
    except Exception as e:
        print(f"OCR处理出错: {e}")
        ocr_results = []
    # 2. 获取图片尺寸
    img = Image.open(image_path)
    img_w, img_h = img.size
    # 3. 转换为 LayoutLM 输入
    words, boxes = ocr_results_to_layoutlm_inputs(ocr_results, img_w, img_h)
    # 4. LayoutLM结构化抽取
    fields = extract_fields_with_layoutlm(words, boxes)
    return fields, ocr_results

def extract_field_by_regex(text, regex):
    match = re.search(regex, text)
    if match:
        return match.group(1).strip()
    return ""

def extract_field_by_ocr_lines(ocr_results, field_name):
    for idx, item in enumerate(ocr_results):
        if field_name in item['text']:
            # 取下一个非空文本
            for j in range(idx+1, len(ocr_results)):
                next_text = ocr_results[j]['text'].strip()
                if next_text:
                    return next_text
    return ""

def box_to_rect(box):
    """将OCR的4点坐标转换为[x0, y0, x1, y1]格式"""
    if isinstance(box[0], (list, tuple)):
        xs = [p[0] for p in box]
        ys = [p[1] for p in box]
        return [int(min(xs)), int(min(ys)), int(max(xs)), int(max(ys))]
    return [int(coord) for coord in box]

def get_field_value_box(ocr_results, field_name):
    """获取字段值的 box，使用智能单行/多行判断"""
    for i, item in enumerate(ocr_results):
        full_text = item['text']
        if full_text.startswith(field_name):

            
            # 查找分隔符位置
            separator_pos = -1
            if field_name + ':' in full_text:
                separator_pos = full_text.find(':')
                print(f"DEBUG: 找到英文冒号，位置: {separator_pos}")
            elif field_name + '：' in full_text:  # 全角冒号
                separator_pos = full_text.find('：')
                print(f"DEBUG: 找到全角冒号，位置: {separator_pos}")
            
            print(f"DEBUG: 分隔符位置: {separator_pos}")
            
            if separator_pos != -1:
                # 获取OCR box
                box = box_to_rect(item['box'])
                x0, y0, x1, y1 = box
                total_width = x1 - x0
                total_height = y1 - y0
                
                # 计算字段值部分
                field_value_part = full_text[separator_pos + 1:]  # 字段值部分
                
                # 智能判断：先检查同一行右侧是否有内容
                if field_value_part.strip():
                    # 字段名右侧有内容，按单行处理

                    return process_single_line_field(item, field_name, separator_pos, field_value_part)
                else:
                    # 字段名右侧无内容，检查是否有独立的右侧文本块
                    right_content_box = find_right_content_box(ocr_results, i, x1, y0, y1)
                    if right_content_box:
                        # 找到右侧内容，按单行处理

                        return right_content_box
                    else:
                        # 没有右侧内容，按多行处理

                        return process_multi_line_field(ocr_results, i, field_name, item)
            else:
                pass
    
    return None

def find_right_content_box(ocr_results, title_index, title_x1, title_y0, title_y1):
    """查找字段名右侧的独立内容块"""
    title_item = ocr_results[title_index]
    title_box = box_to_rect(title_item['box'])
    
    # 查找与字段名在同一行且在其右侧的文本块
    for i, item in enumerate(ocr_results):
        if i == title_index:  # 跳过自己
            continue
            
        item_box = box_to_rect(item['box'])
        item_x0, item_y0, item_x1, item_y1 = item_box
        
        # 检查是否在同一行（y坐标重叠）且在字段名右侧
        y_overlap = (item_y0 <= title_y1 and item_y1 >= title_y0)
        x_right = item_x0 > title_x1 - 20  # 允许20像素的误差
        
        if y_overlap and x_right:
            # 检查是否与字段名太近（可能是同一个文本块的一部分）
            x_distance = item_x0 - title_x1
            if 5 <= x_distance <= 200:  # 合理的距离范围

                # 返回右侧内容块的box，使用右侧内容块的y坐标，稍微扩展以确保完全覆盖
                # 调整y坐标，确保马赛克框与文本基线对齐
                item_height = item_y1 - item_y0
                baseline_offset = int(item_height * 0.3)  # 向下偏移30%的行高
                adjusted_y0 = item_y0 + baseline_offset
                adjusted_y1 = item_y1 + baseline_offset
                return [item_x0 - 5, adjusted_y0, item_x1 + 10, adjusted_y1]
    
    return None

def process_single_line_field(item, field_name, separator_pos, field_value_part):
    """处理单行字段值"""
    full_text = item['text']
    box = box_to_rect(item['box'])
    x0, y0, x1, y1 = box
    total_width = x1 - x0
    total_height = y1 - y0
    
    # 使用改进的字符宽度计算方法
    field_name_part = full_text[:separator_pos + 1]  # 包括分隔符
    
    # 更精确的字符宽度计算
    def get_char_width(char):
        if '\u4e00' <= char <= '\u9fff':  # 中文字符
            return 2.8  # 中文字符更宽，更符合实际显示
        elif char in '：:':  # 冒号
            return 1.8  # 冒号稍宽
        elif char in '，。、':  # 中文标点
            return 2.2
        else:  # 英文字符、数字、其他标点
            return 1.0
    
    # 计算各部分宽度
    field_name_width = sum(get_char_width(char) for char in field_name_part)
    field_value_width = sum(get_char_width(char) for char in field_value_part)
    total_char_width = field_name_width + field_value_width
    
    if total_char_width > 0:
        # 基于字符宽度比例计算像素位置
        # 马赛克框从冒号结束位置开始（字段名结束位置）
        field_name_pixel_width = int((total_width * field_name_width) / total_char_width)
        # 添加小的偏移量确保冒号不被覆盖
        value_x0 = x0 + field_name_pixel_width + 5
        
        # 调整垂直位置，确保马赛克框与文本基线对齐
        # 通常文本的基线在文本框的下半部分，我们稍微向下调整
        baseline_offset = int(total_height * 0.6)  # 向下偏移60%的行高
        adjusted_y0 = y0 + baseline_offset
        adjusted_y1 = y1 + baseline_offset
        
        # 计算字段值的像素宽度
        field_value_pixel_width = int((total_width * field_value_width) / total_char_width)
        # 添加更大的扩展量确保完全覆盖
        value_x1 = value_x0 + field_value_pixel_width + 30
        

        return [int(value_x0), int(adjusted_y0), int(value_x1), int(adjusted_y1)]
    
    return None

def process_multi_line_field(ocr_results, title_index, field_name, title_item):
    """处理多行字段值（如影像诊断）"""

    
    # 获取标题行的位置信息
    title_box = box_to_rect(title_item['box'])
    title_x0, title_y0, title_x1, title_y1 = title_box
    
    # 查找后续的内容行
    content_boxes = []
    content_texts = []
    for i in range(title_index + 1, len(ocr_results)):
        item = ocr_results[i]
        item_box = box_to_rect(item['box'])
        item_x0, item_y0, item_x1, item_y1 = item_box
        
        # 检查是否是内容行（在标题行下方，且不是新的字段标题）
        if (item_y0 > title_y1 and  # 在标题行下方
            not any(item['text'].startswith(field) for field in ['姓名', '性别', '年龄', '科室', '检查日期', '检查设备', '扫描序列', '影像所见', '影像诊断', '诊断结果', '简要病史', 'SUV', 'PET中心地址', '电话', '第'])):
            
            # 检查是否还在合理范围内（避免跨到其他部分）
            if item_y0 - title_y1 < 300:  # 限制在300像素范围内，避免包含页面底部信息
                content_boxes.append(item_box)
                content_texts.append(item['text'])

            else:
                break
        elif item_y0 > title_y1 + 300:  # 超出范围，停止查找
            break
    
    # 根据句号和逗号判断内容完整性
    if content_boxes and content_texts:
        # 合并所有内容文本
        full_content = ''.join(content_texts)

        
        # 检查是否以句号结尾
        if full_content.endswith('。'):
            pass  # 内容完整
        else:
            # 检查最后一个逗号的位置
            last_comma_pos = full_content.rfind('，')
            if last_comma_pos != -1:
                # 如果最后一个逗号后面没有句号，说明内容不完整
                if '。' not in full_content[last_comma_pos:]:
                    # 可以在这里添加逻辑来处理不完整的内容
                    pass
                else:
                    pass
            else:
                pass

    
    if content_boxes:
        # 计算包含所有内容行的矩形框
        min_x = min(box[0] for box in content_boxes)
        min_y = min(box[1] for box in content_boxes)
        max_x = max(box[2] for box in content_boxes)
        max_y = max(box[3] for box in content_boxes)
        
        # 计算字段名的像素宽度，确保马赛克框从字段名结束位置开始
        title_text = title_item['text']
        total_width = title_x1 - title_x0
        
        # 更精确的字符宽度计算
        def get_char_width(char):
            if '\u4e00' <= char <= '\u9fff':  # 中文字符
                return 2.8  # 中文字符更宽，更符合实际显示
            elif char in '：:':  # 冒号
                return 1.8  # 冒号稍宽
            elif char in '，。、':  # 中文标点
                return 2.2
            else:  # 英文字符、数字、其他标点
                return 1.0
        
        # 计算字段名宽度
        field_name_width = sum(get_char_width(char) for char in title_text)
        # 标题行的总字符宽度就是字段名宽度，因为标题行只包含字段名
        total_char_width = field_name_width
        # 计算字段名在标题行中的像素宽度比例
        field_name_pixel_width = int((total_width * field_name_width) / total_char_width)
        
        # 如果计算结果不合理，使用标题框的实际宽度作为字段名宽度
        if field_name_pixel_width >= total_width:
            field_name_pixel_width = total_width - 10  # 留一些边距
        
        # 确保马赛克框从字段名结束位置开始，不覆盖字段名
        # 使用标题框的实际结束位置作为马赛克框的起始位置
        mosaic_start_x = title_x1 + 5  # 从标题框结束位置开始，加5像素边距
        
        # 但是要确保马赛克框能覆盖所有内容行，包括那些起始位置比字段名结束位置更靠左的行
        # 使用内容行的最小x坐标作为马赛克框的起始位置，但要确保不覆盖字段名
        # 如果内容行的最小x坐标比字段名结束位置更小，那么马赛克框应该从字段名结束位置开始
        actual_start_x = max(mosaic_start_x, min_x - 10)  # 取较大值，确保不覆盖字段名
        
        # 额外的安全检查：确保马赛克框不会覆盖字段名
        if actual_start_x < title_x1 + 10:  # 增加更多边距，确保不覆盖字段名
            actual_start_x = title_x1 + 15  # 强制从字段名结束位置开始，增加更多边距
        

        
        # 根据句号位置确定马赛克框的结束位置
        if content_texts:
            full_content = ''.join(content_texts)
            # 找到最后一个句号的位置
            last_period_pos = full_content.rfind('。')
            if last_period_pos != -1:

                # 计算句号在最后一行的位置
                current_pos = 0
                target_line_index = -1
                target_char_pos = -1
                
                for i, text in enumerate(content_texts):
                    if current_pos <= last_period_pos < current_pos + len(text):
                        target_line_index = i
                        target_char_pos = last_period_pos - current_pos
                        break
                    current_pos += len(text)
                

                
                if target_line_index != -1:
                    # 计算句号在该行中的像素位置
                    target_line_text = content_texts[target_line_index]
                    target_line_box = content_boxes[target_line_index]
                    

                    
                    # 计算句号前的字符宽度
                    char_width_sum = 0
                    for char in target_line_text[:target_char_pos + 1]:  # 包含句号
                        char_width_sum += get_char_width(char)
                    
                    # 计算句号的像素位置
                    line_width = target_line_box[2] - target_line_box[0]
                    total_line_char_width = sum(get_char_width(char) for char in target_line_text)
                    
                    # 如果句号在行首，直接使用行起始位置
                    if target_char_pos == 0:
                        period_pixel_pos = 0
                    else:
                        period_pixel_pos = int((line_width * char_width_sum) / total_line_char_width)
                    
                    # 如果计算结果不合理，使用行的实际结束位置
                    if period_pixel_pos < 200:  # 如果句号位置太小，使用行的结束位置
                        period_pixel_pos = line_width - 50  # 使用行结束位置减去50像素边距
                    
                    # 使用句号位置作为马赛克框的结束位置
                    mosaic_end_x = target_line_box[0] + period_pixel_pos + 200  # 加200像素边距
                    
                    # 确保马赛克框覆盖所有内容行，使用最大x坐标作为备选
                    if mosaic_end_x < max_x + 50:  # 如果句号位置不够，使用最大x坐标
                        mosaic_end_x = max_x + 50

                else:
                    # 如果找不到句号位置，使用默认的右边距
                    mosaic_end_x = max_x + 50

            else:
                # 如果没有句号，检查最后一个逗号
                last_comma_pos = full_content.rfind('，')
                if last_comma_pos != -1:

                    # 类似的计算逻辑，但使用逗号位置
                    current_pos = 0
                    target_line_index = -1
                    target_char_pos = -1
                    
                    for i, text in enumerate(content_texts):
                        if current_pos <= last_comma_pos < current_pos + len(text):
                            target_line_index = i
                            target_char_pos = last_comma_pos - current_pos
                            break
                        current_pos += len(text)
                    
                    if target_line_index != -1:
                        target_line_text = content_texts[target_line_index]
                        target_line_box = content_boxes[target_line_index]
                        
                        char_width_sum = 0
                        for char in target_line_text[:target_char_pos + 1]:
                            char_width_sum += get_char_width(char)
                        
                        line_width = target_line_box[2] - target_line_box[0]
                        comma_pixel_pos = int((line_width * char_width_sum) / len(target_line_text))
                        
                        mosaic_end_x = target_line_box[0] + comma_pixel_pos + 30  # 加30像素边距

                    else:
                        mosaic_end_x = max_x + 50

                else:
                    # 既没有句号也没有逗号，使用默认的右边距
                    mosaic_end_x = max_x + 50

        else:
            # 没有内容文本，使用默认的右边距
            mosaic_end_x = max_x + 50

        
        # 马赛克框应该从字段名结束位置开始，确保不覆盖字段名
        # 添加一些边距
        margin = 5
        result_box = [actual_start_x, min_y - margin - 40, mosaic_end_x, max_y + margin]
        
        # 添加调试信息，显示每行的结束位置

        return result_box
    

    return None

def main():
    parser = argparse.ArgumentParser(description='结构化医学报告抽取')
    parser.add_argument('image_path', help='输入图片路径')
    parser.add_argument('--mosaic-fields', type=str, default='', help='需要打马赛克的字段名（逗号分隔）')
    parser.add_argument('--modify-fields', type=str, default='', help='需要修改的字段，格式：字段名:新文本（多个用逗号分隔，如：姓名:小明,年龄:25）')
    args = parser.parse_args()

    image_path = args.image_path
    mosaic_fields = [f.strip() for f in args.mosaic_fields.split(',') if f.strip()]
    
    # 解析文本修改参数
    text_modifications = []
    if args.modify_fields:
        for modification in args.modify_fields.split(','):
            if ':' in modification:
                field_name, new_text = modification.split(':', 1)
                field_name = field_name.strip()
                new_text = new_text.strip()
                if field_name and new_text:
                    text_modifications.append((field_name, new_text))


    # 1. 加载配置
    config = load_config(CONFIG_PATH)
    print(f"📁 加载配置文件: {CONFIG_PATH}")
    
    # 2. OCR+LayoutLM抽取
    fields, ocr_results = extract_with_paddleocr_and_layoutlm(image_path)
    print(f"🔍 OCR完成，识别到 {len(ocr_results)} 个文本块")
    
    # 3. 拼接所有OCR文本用于主题检测
    ocr_text = '\n'.join([item['text'] for item in ocr_results])
    print(f"📝 OCR文本长度: {len(ocr_text)} 字符")
    
    # 4. 主题检测
    theme = detect_theme(ocr_text, config)
    
    if not theme:
        print("⚠️  警告: 未检测到匹配的主题，使用默认配置")
        # 如果没有匹配的主题，创建一个默认主题
        theme = {
            'name': 'DEFAULT',
            'fields': {
                '送检科室': {'regex': r'(神经内科门诊[\-\w]*|神经内科|内科门诊|放射科|核医学科)'},
                '姓名': {'regex': r'姓名[:：]?\s*([^\n\s]+)'},
                '检查项目': {'regex': r'检查项目[:：]?\s*([^\n\s]+)'},
                '影像诊断': {'regex': r'影像诊断[:：]?\s*([^\n\s]+)'}
            }
        }
    
    theme_name = theme['name']
    print(f"🎯 检测到主题: {theme_name}")
    
    # 5. 获取主题字段配置
    theme_fields = theme.get('fields', {})
    print(f"📋 主题字段数量: {len(theme_fields)}")
    print(f"📋 字段列表: {list(theme_fields.keys())}")
    
    # 6. 根据配置文件的fields配置提取字段内容
    output_fields = {}
    
    for field_name, field_config in theme_fields.items():
        field_value = ""
        field_description = field_config.get('description', '无描述')
        
        print(f"\n🔍 提取字段: {field_name} ({field_description})")
        
        # 优先使用配置文件中的正则表达式
        if 'regex' in field_config:
            regex_pattern = field_config['regex']
            print(f"  📝 使用正则表达式: {regex_pattern}")
            
            # 检查正则表达式是否有捕获组
            if '(' in regex_pattern and ')' in regex_pattern:
                # 有捕获组，提取第一个捕获组的内容
                field_value = extract_field_by_regex(ocr_text, regex_pattern)
                if field_value:
                    print(f"  ✅ 正则匹配成功: {field_name} = {field_value}")
                else:
                    print(f"  ❌ 正则匹配失败")
            else:
                # 没有捕获组，直接匹配整个模式
                import re
                match = re.search(regex_pattern, ocr_text)
                if match:
                    field_value = match.group(0).strip()
                    print(f"  ✅ 正则匹配成功: {field_name} = {field_value}")
                else:
                    print(f"  ❌ 正则匹配失败")
        
        # 如果正则没有匹配到，尝试使用OCR行提取
        if not field_value:
            print(f"  🔍 尝试OCR行提取")
            field_value = extract_field_by_ocr_lines(ocr_results, field_name)
            if field_value:
                print(f"  ✅ OCR行提取成功: {field_name} = {field_value}")
            else:
                print(f"  ❌ OCR行提取失败")
        
        # 最后尝试使用LayoutLM结果
        if not field_value:
            print(f"  🔍 尝试LayoutLM提取")
            field_value = fields.get(field_name, "")
            if field_value:
                print(f"  ✅ LayoutLM提取成功: {field_name} = {field_value}")
            else:
                print(f"  ❌ LayoutLM提取失败")
        
        # 如果所有方法都没有提取到，设置为空字符串
        if not field_value:
            print(f"  ⚠️  字段 '{field_name}' 提取失败，设置为空值")
            field_value = ""
        
        output_fields[field_name] = field_value
    
    # 7. 输出JSON
    output = {
        'theme': {
            'name': theme_name,
            'id': theme.get('id', '')
        },
        'fields': output_fields,
        'ocr_results': ocr_results,
        'config_used': {
            'theme_name': theme_name,
            'field_count': len(theme_fields),
            'extracted_count': len([v for v in output_fields.values() if v]),
            'total_ocr_blocks': len(ocr_results)
        }
    }
    
    print(f"\n📊 提取结果统计:")
    print(f"  - 主题: {theme_name}")
    print(f"  - 配置字段数: {len(theme_fields)}")
    print(f"  - 成功提取: {len([v for v in output_fields.values() if v])}")
    print(f"  - 提取失败: {len([v for v in output_fields.values() if not v])}")
    
    print(json.dumps(output, ensure_ascii=False, indent=2))

    # 8. 处理图片修改（文本修改和马赛克）
    if text_modifications or mosaic_fields:
        # 获取文本修改的box
        text_modification_boxes = []
        for field_name, new_text in text_modifications:
            box = get_field_value_box(ocr_results, field_name)
            if box:
                text_modification_boxes.append((field_name, new_text, box))

        
        # 获取马赛克框
        mosaic_boxes = []
        for field in mosaic_fields:
            box = get_field_value_box(ocr_results, field)
            if box:
                mosaic_boxes.append(box)

        
        # 执行图片处理
        if text_modification_boxes or mosaic_boxes:
            # 确定输出路径
            if text_modifications and mosaic_fields:
                # 既有修改又有马赛克
                if image_path.lower().endswith('.jpg'):
                    output_path = image_path[:-4] + '_modified_mosaic.jpg'
                else:
                    output_path = image_path + '_modified_mosaic.jpg'
            elif text_modifications:
                # 只有修改
                if image_path.lower().endswith('.jpg'):
                    output_path = image_path[:-4] + '_modified.jpg'
                else:
                    output_path = image_path + '_modified.jpg'
            else:
                # 只有马赛克
                if image_path.lower().endswith('.jpg'):
                    output_path = image_path[:-4] + '_mosaic.jpg'
                else:
                    output_path = image_path + '_mosaic.jpg'
            
            # 执行修改和马赛克处理
            modify_and_mosaic_image(image_path, text_modification_boxes, mosaic_boxes, output_path)
        else:
            print('未找到需要处理的字段对应区域')

if __name__ == '__main__':
    try:
        main()
    finally:
        # 确保资源清理
        cleanup_ocr_instance() 