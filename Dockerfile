# OCR Structured Medical Report Extraction and Privacy Protection Service - Dockerfile
FROM python:3.9-slim

# Maintainer information
LABEL maintainer="ocr-service"
LABEL description="OCR Structured Medical Report Extraction and Privacy Protection Service"

# Set working directory
WORKDIR /app

# Set environment variables
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV FLASK_APP=structured_report/api.py
ENV FLASK_ENV=production
ENV PIP_DISABLE_PIP_VERSION_CHECK=1
ENV PIP_NO_CACHE_DIR=1

# Use China mirrors for faster package installation
RUN sed -i 's|deb.debian.org|mirrors.aliyun.com|g' /etc/apt/sources.list.d/debian.sources && \
    sed -i 's|security.debian.org|mirrors.aliyun.com|g' /etc/apt/sources.list.d/debian.sources

# Install system dependencies in one layer with cleanup
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    libgl1-mesa-dev \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    libgcc-s1 \
    libjpeg62-turbo-dev \
    libpng-dev \
    libtiff5-dev \
    libfreetype6-dev \
    liblcms2-dev \
    libwebp-dev \
    libharfbuzz-dev \
    libfribidi-dev \
    libxcb1-dev \
    wget \
    curl \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* \
    && rm -rf /tmp/* \
    && rm -rf /var/tmp/*

# Copy requirements first for better caching
COPY requirements.txt .

# 使用最稳定的镜像源，减少超时和重试
RUN pip config set global.index-url https://pypi.douban.com/simple/ && \
    pip config set global.trusted-host pypi.douban.com && \
    pip config set global.timeout 60 && \
    pip config set global.retries 2 && \
    pip install --no-cache-dir --upgrade pip

# 一次性安装所有依赖，减少网络请求
RUN pip install --no-cache-dir -r requirements.txt

# 预下载PaddleOCR模型文件
RUN python -c "
import os
import sys
os.environ['PADDLE_VERBOSE'] = '0'
os.environ['FLAGS_use_mkldnn'] = '0'
os.environ['CUDA_VISIBLE_DEVICES'] = '-1'
try:
    from paddleocr import PaddleOCR
    print('正在下载PaddleOCR模型...')
    ocr = PaddleOCR(lang='ch', use_angle_cls=False, det_limit_side_len=960, show_log=False)
    print('PaddleOCR模型下载完成')
except Exception as e:
    print(f'PaddleOCR模型下载失败: {e}')
    sys.exit(1)
"

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p temp_uploads logs

# Set permissions
RUN chmod +x run_api.py

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:5000/health || exit 1

# Expose port
EXPOSE 5000

# Start command
CMD ["python", "structured_report/api.py"]