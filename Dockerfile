# OCR Structured Medical Report Extraction and Privacy Protection Service - Dockerfile
FROM python:3.9-slim

# Maintainer information
LABEL maintainer="ocr-service"
LABEL description="OCR Structured Medical Report Extraction and Privacy Protection Service"

# Set working directory
WORKDIR /app

# Set environment variables
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV FLASK_APP=structured_report/api.py
ENV FLASK_ENV=production

# Use China mirrors for faster package installation
RUN sed -i 's|deb.debian.org|mirrors.aliyun.com|g' /etc/apt/sources.list.d/debian.sources && \
    sed -i 's|security.debian.org|mirrors.aliyun.com|g' /etc/apt/sources.list.d/debian.sources

# Install system dependencies including build tools and image libraries
RUN apt-get update && apt-get install -y \
    build-essential \
    libgl1-mesa-dri \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    libgcc-s1 \
    libjpeg-dev \
    libpng-dev \
    libtiff-dev \
    libfreetype6-dev \
    liblcms2-dev \
    libwebp-dev \
    libharfbuzz-dev \
    libfribidi-dev \
    libxcb1-dev \
    wget \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install dependencies
COPY requirements.txt .

# Configure pip to use Chinese mirrors with timeout settings
RUN pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple/ && \
    pip config set global.extra-index-url "https://pypi.douban.com/simple/ https://mirrors.aliyun.com/pypi/simple/" && \
    pip config set global.timeout 600 && \
    pip config set global.trusted-host "pypi.tuna.tsinghua.edu.cn pypi.douban.com mirrors.aliyun.com" && \
    pip install --no-cache-dir --upgrade pip

# Install dependencies with retries and increased timeout
RUN pip install --no-cache-dir --timeout=600 --retries=5 -r requirements.txt

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p temp_uploads logs

# Set permissions
RUN chmod +x run_api.py

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:5000/health || exit 1

# Expose port
EXPOSE 5000

# Start command
CMD ["python", "structured_report/api.py"]