# OCR Structured Medical Report Extraction and Privacy Protection Service - Dockerfile
FROM python:3.9-slim

# Maintainer information
LABEL maintainer="ocr-service"
LABEL description="OCR Structured Medical Report Extraction and Privacy Protection Service"

# Set working directory
WORKDIR /app

# Set environment variables
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV FLASK_APP=structured_report/api.py
ENV FLASK_ENV=production
ENV PIP_DISABLE_PIP_VERSION_CHECK=1
ENV PIP_NO_CACHE_DIR=1

# Use China mirrors for faster package installation
RUN sed -i 's|deb.debian.org|mirrors.aliyun.com|g' /etc/apt/sources.list.d/debian.sources && \
    sed -i 's|security.debian.org|mirrors.aliyun.com|g' /etc/apt/sources.list.d/debian.sources

# Install system dependencies in one layer with cleanup
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    libgl1-mesa-dev \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    libgcc-s1 \
    libjpeg62-turbo-dev \
    libpng-dev \
    libtiff5-dev \
    libfreetype6-dev \
    liblcms2-dev \
    libwebp-dev \
    libharfbuzz-dev \
    libfribidi-dev \
    libxcb1-dev \
    wget \
    curl \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* \
    && rm -rf /tmp/* \
    && rm -rf /var/tmp/*

# Copy requirements first for better caching
COPY requirements.txt .

# Configure pip with multiple mirrors and install in one step
RUN pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple/ && \
    pip config set global.extra-index-url "https://pypi.douban.com/simple/ https://mirrors.aliyun.com/pypi/simple/ https://pypi.mirrors.ustc.edu.cn/simple/" && \
    pip config set global.timeout 1200 && \
    pip config set global.retries 10 && \
    pip config set global.trusted-host "pypi.tuna.tsinghua.edu.cn pypi.douban.com mirrors.aliyun.com pypi.mirrors.ustc.edu.cn" && \
    pip install --no-cache-dir --upgrade pip setuptools wheel

# Install dependencies in batches to avoid timeout
RUN pip install --no-cache-dir --timeout=1200 --retries=10 \
    pillow>=9.0.0 \
    flask>=2.3.0 \
    requests>=2.31.0 \
    pdfplumber>=0.9.0

RUN pip install --no-cache-dir --timeout=1200 --retries=10 \
    torch>=2.0.0 \
    transformers>=4.30.0

RUN pip install --no-cache-dir --timeout=1200 --retries=10 \
    paddlepaddle>=2.4.0 \
    paddleocr>=2.7.0

RUN pip install --no-cache-dir --timeout=1200 --retries=10 \
    opencv-python>=4.5.0 \
    shapely>=1.7.0 \
    pytesseract>=0.3.10

# 预下载PaddleOCR模型文件
RUN python -c "
import os
os.environ['PADDLE_VERBOSE'] = '0'
from paddleocr import PaddleOCR
print('正在下载PaddleOCR模型...')
ocr = PaddleOCR(lang='ch', use_angle_cls=False, det_limit_side_len=960)
print('PaddleOCR模型下载完成')
"

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p temp_uploads logs

# Set permissions
RUN chmod +x run_api.py

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:5000/health || exit 1

# Expose port
EXPOSE 5000

# Start command
CMD ["python", "structured_report/api.py"]