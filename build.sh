#!/bin/bash

# Docker镜像快速构建脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认配置
IMAGE_NAME="structured-report"
TAG="latest"
DOCKERFILE="Dockerfile"
BUILD_CONTEXT="."

# 显示帮助信息
show_help() {
    echo -e "${BLUE}Docker镜像构建脚本${NC}"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -f, --fast          使用快速构建模式 (Dockerfile.fast)"
    echo "  -n, --name NAME     设置镜像名称 (默认: structured-report)"
    echo "  -t, --tag TAG       设置镜像标签 (默认: latest)"
    echo "  -c, --clean         构建前清理Docker缓存"
    echo "  -h, --help          显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                  # 使用默认Dockerfile构建"
    echo "  $0 -f               # 使用快速构建模式"
    echo "  $0 -f -c            # 快速构建并清理缓存"
    echo "  $0 -n myapp -t v1.0 # 自定义镜像名和标签"
}

# 清理Docker缓存
clean_docker() {
    echo -e "${YELLOW}🧹 清理Docker缓存...${NC}"
    docker system prune -f
    docker builder prune -f
    echo -e "${GREEN}✅ Docker缓存清理完成${NC}"
}

# 检查Docker是否运行
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        echo -e "${RED}❌ Docker未运行，请先启动Docker${NC}"
        exit 1
    fi
}

# 构建镜像
build_image() {
    local dockerfile=$1
    local image_name=$2
    local tag=$3
    
    echo -e "${BLUE}🚀 开始构建Docker镜像...${NC}"
    echo -e "${BLUE}📋 构建信息:${NC}"
    echo -e "  Dockerfile: ${dockerfile}"
    echo -e "  镜像名称: ${image_name}:${tag}"
    echo -e "  构建上下文: ${BUILD_CONTEXT}"
    echo ""
    
    # 记录开始时间
    start_time=$(date +%s)
    
    # 构建镜像
    if docker build -f "${dockerfile}" -t "${image_name}:${tag}" "${BUILD_CONTEXT}"; then
        # 计算构建时间
        end_time=$(date +%s)
        duration=$((end_time - start_time))
        minutes=$((duration / 60))
        seconds=$((duration % 60))
        
        echo ""
        echo -e "${GREEN}🎉 镜像构建成功！${NC}"
        echo -e "${GREEN}⏱️  构建时间: ${minutes}分${seconds}秒${NC}"
        echo -e "${GREEN}📦 镜像: ${image_name}:${tag}${NC}"
        
        # 显示镜像信息
        echo ""
        echo -e "${BLUE}📊 镜像信息:${NC}"
        docker images "${image_name}:${tag}" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
        
        return 0
    else
        echo -e "${RED}❌ 镜像构建失败${NC}"
        return 1
    fi
}

# 测试镜像
test_image() {
    local image_name=$1
    local tag=$2
    
    echo ""
    echo -e "${BLUE}🧪 测试镜像...${NC}"
    
    # 启动容器进行测试
    container_id=$(docker run -d -p 5000:5000 "${image_name}:${tag}")
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 容器启动成功，ID: ${container_id}${NC}"
        echo -e "${YELLOW}⏳ 等待服务启动...${NC}"
        sleep 10
        
        # 测试健康检查
        if curl -f http://localhost:5000/health > /dev/null 2>&1; then
            echo -e "${GREEN}✅ 健康检查通过${NC}"
            echo -e "${GREEN}🌐 服务可访问: http://localhost:5000${NC}"
        else
            echo -e "${YELLOW}⚠️  健康检查失败，但容器已启动${NC}"
        fi
        
        # 停止测试容器
        docker stop "${container_id}" > /dev/null
        docker rm "${container_id}" > /dev/null
        echo -e "${BLUE}🛑 测试容器已清理${NC}"
    else
        echo -e "${RED}❌ 容器启动失败${NC}"
        return 1
    fi
}

# 主函数
main() {
    local clean_cache=false
    local fast_mode=false
    local run_test=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -f|--fast)
                fast_mode=true
                DOCKERFILE="Dockerfile.fast"
                shift
                ;;
            -n|--name)
                IMAGE_NAME="$2"
                shift 2
                ;;
            -t|--tag)
                TAG="$2"
                shift 2
                ;;
            -c|--clean)
                clean_cache=true
                shift
                ;;
            --test)
                run_test=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                echo -e "${RED}未知选项: $1${NC}"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 检查Docker
    check_docker
    
    # 清理缓存
    if [ "$clean_cache" = true ]; then
        clean_docker
    fi
    
    # 检查Dockerfile是否存在
    if [ ! -f "$DOCKERFILE" ]; then
        echo -e "${RED}❌ Dockerfile不存在: $DOCKERFILE${NC}"
        exit 1
    fi
    
    # 构建镜像
    if build_image "$DOCKERFILE" "$IMAGE_NAME" "$TAG"; then
        # 运行测试
        if [ "$run_test" = true ]; then
            test_image "$IMAGE_NAME" "$TAG"
        fi
        
        echo ""
        echo -e "${GREEN}🎯 构建完成！${NC}"
        echo -e "${GREEN}🚀 启动命令: docker run -p 5000:5000 ${IMAGE_NAME}:${TAG}${NC}"
    else
        exit 1
    fi
}

# 运行主函数
main "$@"
