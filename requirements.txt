# 结构化医学报告指标提取与隐私保护 - 依赖包列表（优化版本）
# 使用固定版本号避免依赖冲突，提高构建稳定性

# 基础依赖
pillow==9.5.0                    # 图像处理
numpy==1.24.3                   # 数值计算

# Web API服务
flask==2.3.2                    # Web框架
requests==2.31.0                # HTTP客户端库
werkzeug==2.3.6                 # WSGI工具库

# PDF处理
pdfplumber==0.9.0               # PDF文本提取

# 深度学习框架
torch==2.0.1                   # PyTorch深度学习框架
torchvision==0.15.2             # PyTorch视觉库

# Transformers
transformers==4.30.2           # Hugging Face模型库
tokenizers==0.13.3             # 分词器

# PaddlePaddle生态
paddlepaddle==2.5.1            # PaddlePaddle深度学习框架
paddleocr==2.7.0.3             # 中文OCR识别

# 图像处理
opencv-python-headless==4.8.0.74  # OpenCV（无GUI版本）
shapely==2.0.1                 # 几何计算

# OCR备选方案
pytesseract==0.3.10            # Tesseract OCR（备选）