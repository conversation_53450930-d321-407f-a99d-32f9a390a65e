#!/bin/bash

# 构建和测试Docker镜像的脚本

set -e  # 遇到错误立即退出

echo "🚀 开始构建Docker镜像..."

# 构建镜像
docker build -t structured-report:test .

echo "✅ 镜像构建完成"

echo "🔍 测试PaddleOCR导入..."

# 测试PaddleOCR导入
docker run --rm structured-report:test python test_paddle_import.py

echo "🌐 启动容器测试API..."

# 启动容器（后台运行）
CONTAINER_ID=$(docker run -d -p 5000:5000 structured-report:test)

echo "📋 容器ID: $CONTAINER_ID"

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 测试健康检查
echo "🏥 测试健康检查..."
if curl -f http://localhost:5000/health; then
    echo "✅ 健康检查通过"
else
    echo "❌ 健康检查失败"
    docker logs $CONTAINER_ID
    docker stop $CONTAINER_ID
    exit 1
fi

# 测试API根路径
echo "🌐 测试API根路径..."
if curl -f http://localhost:5000/; then
    echo "✅ API根路径访问成功"
else
    echo "❌ API根路径访问失败"
    docker logs $CONTAINER_ID
    docker stop $CONTAINER_ID
    exit 1
fi

echo "🎉 所有测试通过！"

# 停止容器
echo "🛑 停止测试容器..."
docker stop $CONTAINER_ID

echo "✨ 测试完成，镜像可以正常使用"
