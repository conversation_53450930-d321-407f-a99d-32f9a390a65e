# 结构化医学报告指标提取与隐私保护

本项目用于从外院导入的医学检查报告单（图片或PDF）中自动提取结构化的指标数据信息，支持智能字段识别、隐私信息马赛克处理、字段值修改和完整的HTTP API服务。

## 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 命令行使用
```bash
# 基础提取
python structured_report/main.py uploads/2.jpg

# 字段修改
python structured_report/main.py uploads/2.jpg --modify-fields "住院号:TEST123"

# 马赛克处理
python structured_report/main.py uploads/2.jpg --mosaic-fields "姓名"
```

### 3. HTTP API服务
```bash
# 启动API服务
python run_api.py

# 测试API（新终端）
python api_client_example.py
```

## 主要功能

### **智能文本识别与提取**
- 支持图片（jpg/png等）和PDF报告的OCR识别与文本提取
- 自动抽取病案号、送检医院、送检科室、临床诊断、检查项目、显像剂、剂量、检查日期、指标项等核心字段
- 支持医学部位错别字、拼音、英文等多样化容错
- 结构化输出JSON，便于后续数据处理

### **智能字段判断**
- **智能单行/多行判断**：自动识别字段是单行还是多行内容
- **精确字段定位**：准确区分字段名和字段值，避免误覆盖
- **多行内容聚合**：智能收集多行字段的完整内容
- **句号/逗号完整性检测**：基于标点符号判断内容完整性

###  **隐私信息马赛克**
- 支持指定字段的隐私信息马赛克处理
- 精确的马赛克框定位，确保完全覆盖敏感信息
- 智能调整马赛克框位置，避免覆盖字段名
- 支持批量字段马赛克处理

### **智能字段值修改**
- 支持在图片上直接修改指定字段的文本内容
- 智能字体选择，自动适配中文字体显示
- 精确的文本定位和替换，保持原图片格式
- 支持单行和多行字段的智能修改
- 可与马赛克功能同时使用

### **HTTP API服务**
- 提供完整的RESTful API接口
- 支持文件上传和处理结果下载
- 支持文档提取、字段修改、马赛克处理等所有功能
- 异步任务处理，支持大文件处理
- 完整的错误处理和状态管理
- 自动临时文件清理机制

### **性能优化**
- **PaddleOCR和LayoutLM模型全局只加载一次**，速度大幅提升
- **LayoutLM模型已本地化**，无需每次从网络下载
- **适配Mac（Apple Silicon）MPS加速**
- **结构化字段提取支持正则优先、跨行提取等多策略**，准确率高

## 依赖环境

- **Python**: 3.7+ (推荐 3.8+)
- **操作系统**: Windows, macOS, Linux
- **内存**: 建议 4GB+ (模型加载需要约 2GB)
- **存储**: 至少 1GB 可用空间 (模型文件约 430MB)

### 核心依赖
- **pillow**: 图像处理
- **paddleocr**: 中文OCR识别 (推荐)
- **transformers**: Hugging Face模型库
- **torch**: PyTorch深度学习框架
- **pdfplumber**: PDF文本提取
- **pytesseract**: Tesseract OCR (备选)

## 安装依赖

### 快速安装
```bash
pip install -r requirements.txt
```

### 详细安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd structured-report
```

2. **创建虚拟环境（推荐）**
```bash
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或
venv\Scripts\activate     # Windows
```

3. **安装Python依赖**
```bash
pip install -r requirements.txt
```

4. **安装系统依赖（可选）**
   
   如果使用pytesseract作为备选OCR：
   ```bash
   # macOS
   brew install tesseract
   
   # Ubuntu/Debian
   sudo apt-get install tesseract-ocr
   
   # Windows
   # 下载安装包: https://github.com/UB-Mannheim/tesseract/wiki
   ```

> **推荐**：为获得更高的中文医学报告识别率，请确保本地已安装 [PaddleOCR](https://github.com/PaddlePaddle/PaddleOCR)。

## 模型文件

项目已包含本地化的 LayoutLM 模型文件：
- **模型路径**：`./models/layoutlm-base-uncased/`
- **模型大小**：约 430MB
- **自动加载**：程序启动时自动从本地加载，无需网络连接

> **注意**：模型文件已添加到 `.gitignore` 中，不会提交到版本控制系统。模型已预下载到本地，无需网络连接。
> 
> 安装命令：
> ```bash
> pip install paddleocr
> ```
> 
> 若未安装 PaddleOCR，将自动降级使用 pytesseract。

## HTTP API服务

### 快速启动API服务
```bash
# 方式1：使用启动脚本（推荐）
python run_api.py

# 方式2：直接启动
python structured_report/api.py
```

服务将在 `http://localhost:5000` 启动，提供以下接口：

### API接口说明

#### 0. 服务信息和文档
```
GET /         # API服务信息
GET /docs     # 完整API文档
```

#### 1. 健康检查
```
GET /health
```

#### 2. 文档结构化提取
```
POST /extract
Content-Type: multipart/form-data

参数:
- file: 上传的图片或PDF文件
- config: 可选的配置文件内容（JSON格式）
```

#### 3. 字段值修改
```
POST /modify
Content-Type: multipart/form-data

参数:
- file: 上传的图片文件
- modifications: 修改配置，格式为 {"字段名": "新值", ...}
```

#### 4. 马赛克处理
```
POST /mosaic
Content-Type: multipart/form-data

参数:
- file: 上传的图片文件
- fields: 需要马赛克的字段列表，格式为 ["字段名1", "字段名2", ...]
```

#### 5. 综合处理
```
POST /process
Content-Type: multipart/form-data

参数:
- file: 上传的图片文件
- modifications: 可选，修改配置
- mosaic_fields: 可选，需要马赛克的字段列表
```

#### 6. 配置文件管理
```
GET /config                    # 获取当前配置文件
PUT /config                    # 更新配置文件
GET /config/backup            # 获取配置备份列表
POST /config/restore          # 恢复配置文件备份
```

#### 7. 文件下载
```
GET /download/<task_id>/<filename>
```

#### 8. 清理临时文件
```
DELETE /cleanup/<task_id>
```

### API客户端示例
```bash
# 测试所有API接口（确保API服务已启动）
python api_client_example.py
```

### 配置管理工具
```bash
# 显示当前配置信息
python config_manager.py show

# 更新配置文件
python config_manager.py update --file new_config.json

# 查看配置备份列表
python config_manager.py backup

# 恢复配置备份
python config_manager.py restore --backup extract_config_backup_20241201_120000.json

# 创建示例配置文件
python config_manager.py sample
```

> **注意**: 运行客户端示例前，请确保API服务已启动：`python run_api.py`

### API使用示例

#### Python客户端
```python
import requests
import json

# 文档提取
with open('document.jpg', 'rb') as f:
    response = requests.post('http://localhost:5000/extract', files={'file': f})
    result = response.json()
    print(result['extracted_fields'])

# 字段修改
modifications = {"病案号": "12345", "送检医院": "XX医院"}
with open('document.jpg', 'rb') as f:
    data = {'modifications': json.dumps(modifications)}
    response = requests.post('http://localhost:5000/modify', files={'file': f}, data=data)
    task_id = response.json()['task_id']

# 配置文件管理
# 获取当前配置
response = requests.get('http://localhost:5000/config')
current_config = response.json()['config']

# 更新配置
new_config = {
    "themes": [
        {
            "name": "CUSTOM_THEME",
            "match_keywords": ["自定义", "CUSTOM"],
            "fields": {
                "自定义字段": {"regex": "自定义[:：]?\\s*([^\\n]+)"}
            }
        }
    ]
}
response = requests.put('http://localhost:5000/config', json=new_config)

# 获取配置备份列表
response = requests.get('http://localhost:5000/config/backup')
backups = response.json()['backups']

# 恢复配置备份
restore_data = {"backup_file": backups[0]['filename']}
response = requests.post('http://localhost:5000/config/restore', json=restore_data)

# 下载处理后的图片
response = requests.get(f'http://localhost:5000/download/{task_id}/modified.jpg')
with open('modified_result.jpg', 'wb') as f:
    f.write(response.content)
```

#### cURL示例
```bash
# 查看API服务信息
curl http://localhost:5000/

# 查看完整API文档
curl http://localhost:5000/docs

# 健康检查
curl http://localhost:5000/health

# 文档提取
curl -X POST -F "file=@document.jpg" http://localhost:5000/extract

# 字段修改
curl -X POST \
  -F "file=@document.jpg" \
  -F 'modifications={"病案号":"12345"}' \
  http://localhost:5000/modify

# 配置文件管理
# 获取当前配置
curl http://localhost:5000/config

# 更新配置
curl -X PUT \
  -H "Content-Type: application/json" \
  -d '{"themes":[{"name":"CUSTOM","match_keywords":["自定义"],"fields":{"自定义字段":{"regex":"自定义[:：]?\\s*([^\\n]+)"}}}]}' \
  http://localhost:5000/config

# 获取配置备份列表
curl http://localhost:5000/config/backup

# 恢复配置备份
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"backup_file":"extract_config_backup_20241201_120000.json"}' \
  http://localhost:5000/config/restore
```

## 使用方法

### 基础文本提取

```bash
python structured_report/main.py <输入文件路径>
```

### 隐私信息马赛克

```bash
python structured_report/main.py <输入文件路径> --mosaic-fields "字段名1" "字段名2"
```

### 字段值修改

```bash
python structured_report/main.py <输入文件路径> --modify-fields "字段名1:新文本1,字段名2:新文本2"
```

### 命令行参数

```bash
python structured_report/main.py [选项] <输入文件路径>

选项:
  --mosaic-fields TEXT [TEXT ...]  指定需要马赛克的字段名
  --modify-fields TEXT             指定需要修改的字段，格式：字段名:新文本（多个用逗号分隔）
  --output-dir PATH                指定输出目录 (默认: 输入文件所在目录)
  --config PATH                    指定配置文件路径 (默认: extract_config.json)
  --help, -h                       显示帮助信息
```

### 使用示例

```bash
# 提取结构化信息
python structured_report/main.py uploads/1.jpg

# 对指定字段进行马赛克处理
python structured_report/main.py uploads/2.jpg --mosaic-fields "给药途径"
python structured_report/main.py uploads/2.jpg --mosaic-fields "简要病史" "临床诊断"

# 修改指定字段的文本内容
python structured_report/main.py uploads/2.jpg --modify-fields "病案号:12345,送检医院:XX医院"
python structured_report/main.py uploads/2.jpg --modify-fields "检查日期:2025/01/01"

# 同时进行马赛克和文本修改
python structured_report/main.py uploads/2.jpg --mosaic-fields "简要病史" --modify-fields "病案号:12345"

# 指定输出目录
python structured_report/main.py uploads/3.jpg --output-dir ./results

# 批量处理
for file in uploads/*.jpg; do
    python structured_report/main.py "$file"
done
```

### 输入输出说明

#### 支持的文件格式
- **图片**: .jpg, .jpeg, .png, .bmp, .tiff
- **文档**: .pdf
- **文本**: .txt

#### 输出文件
- **JSON文件**: `{原文件名}.json` - 结构化提取结果
- **文本文件**: `{原文件名}.txt` - 纯文本提取结果
- **马赛克图片**: `{原文件名}_mosaic.jpg` - 隐私信息马赛克处理后的图片
- **修改图片**: `{原文件名}_modified.jpg` - 字段值修改后的图片
- **综合处理图片**: `{原文件名}_modified_mosaic.jpg` - 同时进行修改和马赛克处理的图片

#### 输出示例
```json
{
  "theme": "PET_IMAGE_REPORT",
  "fields": {
    "病案号": "12345",
    "送检医院": "XX医院",
    "检查项目": "脑部18F-AV1",
    "检查日期": "2025/4/22",
    "显像剂": "18F-Florbetaben",
    "简要病史": "记忆力减退7-8年...",
    "PET中心地址": "上海市徐汇区...",
    "电话": "021-64285263"
  },
  "ocr_results": [...]
}
```

## 智能字段判断特性

### 单行字段处理
- 自动识别字段名右侧是否有内容
- 精确计算字符宽度，准确定位马赛克框
- 避免覆盖字段名，确保只马赛克字段值

### 多行字段处理
- 智能收集与字段名相关的多行内容
- 基于句号/逗号判断内容完整性
- 精确计算马赛克框边界，确保完整覆盖

### 示例场景
```
给药途径：左手          # 单行字段 - 智能识别右侧内容
简要病史：               # 多行字段 - 智能收集下方内容
记忆力减退7-8年，
目前无法自主说话、
不认识人、大小便失禁
```

## 性能优化说明

- **模型复用**：PaddleOCR和LayoutLM模型在程序启动时全局只加载一次，极大提升多文件处理速度
- **MPS加速**：Mac（Apple Silicon）用户无需配置GPU，自动使用MPS加速（PyTorch自动检测）
- **智能策略**：结构化字段提取采用正则优先、跨行提取等多策略，兼容OCR分行、字段错位等实际情况
- **批量处理**：支持批量处理时多进程并行（可选）

## 目录结构

```
structured-report/
├── structured_report/          # 主程序目录
│   ├── main.py                # 主程序入口（命令行）
│   ├── api.py                 # HTTP API服务
│   ├── text_modify_utils.py   # 文本修改工具
│   ├── extract_config.json    # 字段提取配置
│   └── extractor/             # 提取器模块
│       ├── config_loader.py   # 配置加载器
│       ├── layoutlm_extractor.py # LayoutLM模型提取器
│       ├── mosaic_utils.py    # 马赛克处理工具
│       ├── ocr_utils.py       # OCR工具
│       ├── parser.py          # 解析器
│       ├── pdf_utils.py       # PDF处理工具
│       └── theme_detector.py  # 主题检测器
├── models/                    # 模型文件目录
│   └── layoutlm-base-uncased/ # LayoutLM模型（本地化）
├── uploads/                   # 测试文件目录
├── run_api.py                 # API服务启动脚本
├── api_client_example.py      # API客户端示例
├── requirements.txt           # 依赖列表
└── README.md                  # 项目说明


## 更新日志

### v1.0.0 (最新)
- 新增完整的HTTP API服务，支持RESTful接口调用
- 新增智能字段值修改功能，支持在图片上直接修改文本内容
- 智能字体选择，自动适配中文字体显示
- 支持文件上传下载，异步任务处理
- 完整的API客户端示例和文档
- 模型本地化：LayoutLM模型已预下载到本地，支持离线使用
- 依赖优化：更新requirements.txt，添加Flask和requests依赖
- 文档完善：更新README，添加详细API使用说明
- 文件清理：优化项目结构，清理临时文件和测试文件
- 接口修复：修复文件下载和马赛克处理接口问题
- 完整测试：所有API接口测试通过

### v0.9.0
- 新增智能单行/多行字段判断功能
- 新增隐私信息马赛克处理功能
- 优化马赛克框定位算法，支持精确覆盖
- 改进字段完整性检测，基于标点符号判断
- 优化依赖管理，移除未使用的包

### v0.8.0
- 基础OCR识别和结构化提取功能
- PaddleOCR和LayoutLM模型集成
- Mac MPS加速支持

## 常见问题

### Q: 模型下载失败怎么办？
A: 模型已预下载到本地 `./models/` 目录，无需网络连接。如果模型文件损坏，可以删除 `./models/` 目录重新运行程序自动下载。

### Q: 内存不足怎么办？
A: 建议使用4GB+内存。如果内存不足，可以尝试：
- 关闭其他程序释放内存
- 使用更小的图片文件
- 分批处理多个文件

### Q: OCR识别效果不好怎么办？
A: 可以尝试：
- 确保图片清晰度足够
- 检查图片是否倾斜，程序会自动纠偏
- 如果PaddleOCR效果不好，会自动降级使用pytesseract

### Q: 如何自定义提取字段？
A: 修改 `structured_report/extract_config.json` 文件，添加或修改字段配置。

### Q: 如何修改字段的文本内容？
A: 使用 `--modify-fields` 参数，格式为 `字段名:新文本`，多个字段用逗号分隔。例如：
```bash
python structured_report/main.py image.jpg --modify-fields "病案号:12345,送检医院:XX医院"
```

### Q: 可以同时进行马赛克和文本修改吗？
A: 可以！同时使用 `--mosaic-fields` 和 `--modify-fields` 参数即可。程序会先生成修改后的图片，再进行马赛克处理。

### Q: 如何使用HTTP API服务？
A: 
1. 启动API服务：`python run_api.py`
2. 访问 `http://localhost:5000/health` 检查服务状态
3. 使用客户端示例：`python api_client_example.py`
4. 查看API文档了解各接口的使用方法

### Q: API服务支持哪些功能？
A: API服务支持所有命令行功能：
- 文档结构化提取 (`/extract`)
- 字段值修改 (`/modify`) 
- 马赛克处理 (`/mosaic`)
- 综合处理 (`/process`)
- 文件下载和任务管理


- 核心OCR和结构化提取功能
- 智能字段值修改功能  
- 隐私信息马赛克处理
- 完整的HTTP API服务
- 模型本地化部署
- 多种输入格式支持
- 性能优化完成
- 完整的文档和示例
- 所有API接口测试通过
- 项目文件清理完成
- 持续优化和功能扩展中

项目处于活跃开发状态，欢迎社区贡献！

## 项目状态

### 核心文件：
- 源代码文件 (`structured_report/`)
- 配置文件 (`requirements.txt`, `extract_config.json`)
- 文档文件 (`README.md`)
- 测试图片 (`uploads/`)
- 模型文件 (`models/`)

###项目结构优化：
- 总大小：434MB（主要是模型文件）
- 核心代码：约1MB
- 测试图片：约1.7MB
- 模型文件：约430MB

### 配置接口文档

以下为与配置相关的 HTTP API 接口说明，默认 Base URL 为 `http://localhost:5000`。

#### 通用说明
- 所有返回时间均为 ISO8601 格式（如 `2025-01-01T12:00:00.000000`）。
- 失败时通常返回形如 `{ "error": "错误信息" }` 的 JSON，HTTP 状态码根据情况为 400/404/500。

#### 1) 获取当前配置
```
GET /config
```
响应 (200):
```json
{
  "success": true,
  "config": {
    "themes": [
      {
        "name": "CUSTOM_THEME",
        "match_keywords": ["自定义", "CUSTOM"],
        "fields": {
          "自定义字段": {"regex": "自定义[:：]?\\s*([^\\n]+)", "description": "示例描述"}
        }
      }
    ]
  },
  "config_file": "/abs/path/structured_report/extract_config.json",
  "timestamp": "2025-01-01T12:00:00.000000"
}
```
失败 (400):
```json
{"error":"配置文件不存在"}
```

示例 cURL:
```bash
curl http://localhost:5000/config
```

#### 2) 更新配置
```
PUT /config
Content-Type: application/json
```
请求体 JSON 结构（校验规则）：
- **themes**: 数组，必填
  - **id**: 字符串，选填。前端传入的主题标识符
  - **name**: 字符串，必填
  - **match_keywords**: 字符串数组，选填
  - **fields**: 对象，必填。键为字段名，值为字段配置对象：
    - **regex**: 字符串，建议正则表达式
    - **description**: 字符串，选填

请求示例：
```json
{
  "themes": [
    {
      "id": "theme_001",
      "name": "CUSTOM_THEME",
      "match_keywords": ["自定义", "CUSTOM"],
      "fields": {
        "自定义字段": {"regex": "自定义[:：]?\\s*([^\\n]+)", "description": "示例描述"}
      }
    },
    {
      "id": "theme_002",
      "name": "ANOTHER_THEME",
      "fields": {
        "另一个字段": {"regex": "字段[:：]?\\s*([^\\n]+)"}
      }
    }
  ]
}
```

成功 (200):
```json
{
  "success": true,
  "message": "配置文件更新成功",
  "backup": "/abs/path/config_backups/extract_config_backup_20250101_120000.json",
  "theme_ids": [
    {"id": "theme_001", "name": "CUSTOM_THEME"},
    {"id": "theme_002", "name": "ANOTHER_THEME"}
  ]
}
```

失败：
- 400: `{"error":"配置文件缺少必要的 'themes' 字段"}`、`{"error":"'themes' 字段必须是数组"}` 等
- 500: `{"error":"配置文件保存失败: ..."}`

示例 cURL：
```bash
curl -X PUT \
  -H "Content-Type: application/json" \
  -d '{"themes":[{"id":"theme_001","name":"CUSTOM","fields":{"自定义字段":{"regex":"自定义[:：]?\\s*([^\\n]+)"}}}]}' \
  http://localhost:5000/config
```

#### 3) 获取配置备份列表
```
GET /config/backup
```
成功 (200):
```json
{
  "success": true,
  "backups": [
    {
      "filename": "extract_config_backup_20250101_120000.json",
      "size": 1234,
      "created_time": "2025-01-01T12:00:00",
      "modified_time": "2025-01-01T12:00:00"
    }
  ],
  "backup_dir": "/abs/path/config_backups",
  "total_count": 1
}
```

示例 cURL：
```bash
curl http://localhost:5000/config/backup
```

#### 4) 从备份恢复配置
```
POST /config/restore
Content-Type: application/json
```
请求体：
```json
{"backup_file":"extract_config_backup_20250101_120000.json"}
```
成功 (200):
```json
{
  "success": true,
  "message": "配置文件已从备份恢复: extract_config_backup_20250101_120000.json",
  "restored_from": "extract_config_backup_20250101_120000.json",
  "timestamp": "2025-01-01T12:00:05.000000"
}
```
失败：
- 400: 缺少 `backup_file`、或备份文件格式无效
- 404: 备份文件不存在

示例 cURL：
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"backup_file":"extract_config_backup_20250101_120000.json"}' \
  http://localhost:5000/config/restore
```

#### 5) 获取配置模板列表
```
GET /config/templates
```
成功 (200):
```json
{
  "success": true,
  "templates": [
    {
      "name": "TEST_TEMPLATE",
      "description": "这是一个测试配置模板",
      "created_time": "2025-01-01T11:59:00",
      "file_size": 2048,
      "modified_time": "2025-01-01T12:00:00"
    }
  ],
  "total_count": 1
}
```

示例 cURL：
```bash
curl http://localhost:5000/config/templates
```

#### 6) 保存配置模板
```
POST /config/template/save
Content-Type: application/json
```
请求体：
```json
{
  "name": "MY_TEMPLATE",
  "config": {
    "themes": [
      {"name": "T1", "fields": {"字段A": {"regex": "A[:：]?\\s*([^\\n]+)"}}}
    ]
  },
  "description": "可选的模板描述"
}
```
成功 (200):
```json
{
  "success": true,
  "message": "配置模板 'MY_TEMPLATE' 保存成功",
  "template_file": "/abs/path/config_templates/MY_TEMPLATE.json"
}
```
失败：
- 400: 缺少 `name` 或 `config`
- 500: 模板保存失败

示例 cURL：
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"name":"MY_TEMPLATE","config":{"themes":[{"name":"T1","fields":{"字段A":{"regex":"A[:：]?\\s*([^\\n]+)"}}}]},"description":"desc"}' \
  http://localhost:5000/config/template/save
```

#### 7) 应用配置模板
```
POST /config/template/apply
Content-Type: application/json
```
请求体：
```json
{"template_name":"MY_TEMPLATE"}
```
成功 (200):
```json
{
  "success": true,
  "message": "配置模板 'MY_TEMPLATE' 应用成功",
  "template_applied": "MY_TEMPLATE",
  "backup": "/abs/path/config_backups/extract_config_backup_20250101_120100.json",
  "timestamp": "2025-01-01T12:01:00.000000"
}
```

示例 cURL：
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"template_name":"MY_TEMPLATE"}' \
  http://localhost:5000/config/template/apply
```

#### 8) 删除配置模板
```
DELETE /config/template/<template_name>
```
成功 (200):
```json
{"success": true, "message": "配置模板 'MY_TEMPLATE' 删除成功"}
```
不存在 (404):
```json
{"error": "配置模板 'UNKNOWN' 不存在"}
```

示例 cURL：
```bash
curl -X DELETE http://localhost:5000/config/template/MY_TEMPLATE
```

#### 字段提取配置结构参考
```json
{
  "themes": [
    {
      "name": "PET_IMAGE_REPORT",
      "match_keywords": ["PET", "影像报告"],
      "fields": {
        "病案号": {"regex": "病案号[:：]?\\s*([^\\n]+)", "description": "病案号"},
        "检查日期": {"regex": "检查日期[:：]?\\s*([0-9\\-/]+)"}
      }
    }
  ]
}
```

