#!/usr/bin/env python3
"""
初始化PaddleOCR模型
在容器启动时预下载所需的模型文件
"""

import os
import sys
import time

def init_paddleocr_models():
    """初始化PaddleOCR模型"""
    print("🔍 正在初始化PaddleOCR模型...")
    
    try:
        # 设置环境变量
        os.environ['PADDLE_VERBOSE'] = '0'
        os.environ['FLAGS_use_mkldnn'] = '0'
        os.environ['FLAGS_cpu_deterministic'] = '1'
        os.environ['CUDA_VISIBLE_DEVICES'] = '-1'
        
        # 导入PaddleOCR
        from paddleocr import PaddleOCR
        
        # 初始化OCR实例，这会触发模型下载
        print("📥 下载中文OCR模型...")
        ocr = PaddleOCR(
            lang='ch', 
            use_angle_cls=False, 
            det_limit_side_len=960,
            show_log=False
        )
        
        print("✅ PaddleOCR模型初始化完成")
        return True
        
    except Exception as e:
        print(f"❌ PaddleOCR模型初始化失败: {e}")
        return False

def check_network_connection():
    """检查网络连接"""
    import urllib.request
    
    test_urls = [
        'https://paddleocr.bj.bcebos.com',
        'https://paddle-model-ecology.bj.bcebos.com',
        'http://www.baidu.com'
    ]
    
    for url in test_urls:
        try:
            urllib.request.urlopen(url, timeout=10)
            print(f"✅ 网络连接正常: {url}")
            return True
        except Exception as e:
            print(f"❌ 网络连接失败: {url} - {e}")
            continue
    
    return False

def main():
    """主函数"""
    print("=" * 60)
    print("PaddleOCR模型初始化")
    print("=" * 60)
    
    # 检查网络连接
    if not check_network_connection():
        print("⚠️  网络连接异常，可能影响模型下载")
    
    # 初始化模型
    max_retries = 3
    for attempt in range(max_retries):
        print(f"\n🔄 尝试初始化模型 (第{attempt + 1}次/共{max_retries}次)")
        
        if init_paddleocr_models():
            print("\n🎉 模型初始化成功！")
            return 0
        
        if attempt < max_retries - 1:
            wait_time = (attempt + 1) * 10
            print(f"⏳ 等待{wait_time}秒后重试...")
            time.sleep(wait_time)
    
    print("\n❌ 模型初始化失败，请检查网络连接")
    return 1

if __name__ == "__main__":
    sys.exit(main())
