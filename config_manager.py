#!/usr/bin/env python3
"""
配置文件管理工具
提供命令行界面来管理结构化医学报告的配置文件
"""

import os
import json
import argparse
import requests
from datetime import datetime
import sys

# API基础URL
BASE_URL = "http://localhost:5000"

def check_api_status():
    """检查API服务状态"""
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def get_current_config():
    """获取当前配置文件"""
    try:
        response = requests.get(f"{BASE_URL}/config")
        if response.status_code == 200:
            return response.json()
        else:
            print(f"❌ 获取配置失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 获取配置异常: {e}")
        return None

def update_config(config_data):
    """更新配置文件"""
    try:
        response = requests.put(
            f"{BASE_URL}/config",
            json=config_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 配置更新成功")
            print(f"   备份文件: {data.get('backup')}")
            return True
        else:
            print(f"❌ 配置更新失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 配置更新异常: {e}")
        return False

def list_backups():
    """列出配置备份"""
    try:
        response = requests.get(f"{BASE_URL}/config/backup")
        if response.status_code == 200:
            data = response.json()
            print(f"📁 配置备份列表 (共 {data.get('total_count')} 个)")
            print(f"   备份目录: {data.get('backup_dir')}")
            print()
            
            if data.get('backups'):
                for i, backup in enumerate(data['backups'], 1):
                    print(f"{i:2d}. {backup['filename']}")
                    print(f"    大小: {backup['size']} 字节")
                    print(f"    创建时间: {backup['created_time']}")
                    print(f"    修改时间: {backup['modified_time']}")
                    print()
            else:
                print("   暂无备份文件")
            return data.get('backups', [])
        else:
            print(f"❌ 获取备份列表失败: {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ 获取备份列表异常: {e}")
        return []

def restore_config(backup_file):
    """恢复配置文件"""
    try:
        restore_data = {"backup_file": backup_file}
        response = requests.post(
            f"{BASE_URL}/config/restore",
            json=restore_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 配置恢复成功")
            print(f"   恢复自: {data.get('restored_from')}")
            return True
        else:
            print(f"❌ 配置恢复失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 配置恢复异常: {e}")
        return False

def validate_config_file(file_path):
    """验证配置文件格式"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        # 基本结构验证
        if 'themes' not in config_data:
            print("❌ 配置文件缺少 'themes' 字段")
            return None
        
        if not isinstance(config_data['themes'], list):
            print("❌ 'themes' 字段必须是数组")
            return None
        
        # 主题验证
        for i, theme in enumerate(config_data['themes']):
            if not isinstance(theme, dict):
                print(f"❌ 主题 {i+1} 必须是对象")
                return None
            
            if 'name' not in theme:
                print(f"❌ 主题 {i+1} 缺少 'name' 字段")
                return None
            
            if 'fields' not in theme:
                print(f"❌ 主题 {i+1} 缺少 'fields' 字段")
                return None
            
            if not isinstance(theme['fields'], dict):
                print(f"❌ 主题 {i+1} 的 'fields' 必须是对象")
                return None
        
        print("✅ 配置文件格式验证通过")
        return config_data
        
    except FileNotFoundError:
        print(f"❌ 配置文件不存在: {file_path}")
        return None
    except json.JSONDecodeError as e:
        print(f"❌ JSON格式错误: {e}")
        return None
    except Exception as e:
        print(f"❌ 配置文件验证失败: {e}")
        return None

def create_sample_config():
    """创建示例配置文件"""
    sample_config = {
        "themes": [
            {
                "name": "SAMPLE_THEME",
                "match_keywords": ["示例", "SAMPLE"],
                "fields": {
                    "示例字段": {
                        "regex": "示例[:：]?\\s*([^\\n]+)",
                        "description": "这是一个示例字段"
                    }
                }
            }
        ]
    }
    
    filename = f"sample_config_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(sample_config, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 示例配置文件已创建: {filename}")
    return filename

def show_config_info(config_data):
    """显示配置信息"""
    if not config_data or 'config' not in config_data:
        print("❌ 无法获取配置信息")
        return
    
    config = config_data['config']
    print(f"📋 配置文件信息")
    print(f"   配置文件: {config_data.get('config_file')}")
    print(f"   主题数量: {len(config.get('themes', []))}")
    print()
    
    for i, theme in enumerate(config['themes'], 1):
        print(f"主题 {i}: {theme['name']}")
        print(f"  匹配关键词: {', '.join(theme.get('match_keywords', []))}")
        print(f"  字段数量: {len(theme.get('fields', {}))}")
        
        # 显示前几个字段
        fields = list(theme.get('fields', {}).keys())[:5]
        if fields:
            print(f"  字段示例: {', '.join(fields)}")
            if len(theme.get('fields', {})) > 5:
                print(f"  ... 还有 {len(theme.get('fields', {})) - 5} 个字段")
        print()


def list_templates():
    """列出配置模板"""
    try:
        response = requests.get(f"{BASE_URL}/config/templates")
        if response.status_code == 200:
            data = response.json()
            print(f"📁 配置模板列表 (共 {data.get('total_count')} 个)")
            print()
            
            if data.get('templates'):
                for i, template in enumerate(data['templates'], 1):
                    print(f"{i:2d}. {template['name']}")
                    print(f"    描述: {template['description']}")
                    print(f"    创建时间: {template['created_time']}")
                    print(f"    文件大小: {template['file_size']} 字节")
                    print()
            else:
                print("   暂无配置模板")
            return data.get('templates', [])
        else:
            print(f"❌ 获取模板列表失败: {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ 获取模板列表异常: {e}")
        return []

def save_template(template_name, config_file, description=""):
    """保存配置模板"""
    try:
        # 读取配置文件
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        # 保存模板
        template_data = {
            "name": template_name,
            "config": config_data,
            "description": description
        }
        
        response = requests.post(
            f"{BASE_URL}/config/template/save",
            json=template_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 配置模板保存成功")
            print(f"   模板名称: {template_name}")
            print(f"   模板文件: {data.get('template_file')}")
            return True
        else:
            print(f"❌ 配置模板保存失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 保存配置模板异常: {e}")
        return False

def apply_template(template_name):
    """应用配置模板"""
    try:
        apply_data = {"template_name": template_name}
        response = requests.post(
            f"{BASE_URL}/config/template/apply",
            json=apply_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 配置模板应用成功")
            print(f"   应用的模板: {data.get('template_applied')}")
            print(f"   备份文件: {data.get('backup')}")
            return True
        else:
            print(f"❌ 配置模板应用失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 应用配置模板异常: {e}")
        return False

def delete_template(template_name):
    """删除配置模板"""
    try:
        response = requests.delete(f"{BASE_URL}/config/template/{template_name}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 配置模板删除成功")
            print(f"   删除的模板: {template_name}")
            return True
        else:
            print(f"❌ 配置模板删除失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 删除配置模板异常: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='配置文件管理工具')
    parser.add_argument('action', choices=['show', 'update', 'backup', 'restore', 'sample', 'templates', 'save-template', 'apply-template', 'delete-template'], 
                       help='操作类型')
    parser.add_argument('--file', '-f', help='配置文件路径（用于update或save-template操作）')
    parser.add_argument('--backup', '-b', help='备份文件名（用于restore操作）')
    parser.add_argument('--name', '-n', help='模板名称（用于save-template、apply-template、delete-template操作）')
    parser.add_argument('--description', '-d', help='模板描述（用于save-template操作）')
    
    args = parser.parse_args()
    
    # 检查API服务状态
    if not check_api_status():
        print("❌ API服务未运行，请先启动API服务：python run_api.py")
        sys.exit(1)
    
    print("🔧 配置文件管理工具")
    print("=" * 50)
    
    if args.action == 'show':
        # 显示当前配置
        config_data = get_current_config()
        if config_data:
            show_config_info(config_data)
    
    elif args.action == 'update':
        # 更新配置
        if not args.file:
            print("❌ 请指定配置文件路径: --file <配置文件路径>")
            sys.exit(1)
        
        config_data = validate_config_file(args.file)
        if config_data:
            update_config(config_data)
    
    elif args.action == 'backup':
        # 列出备份
        list_backups()
    
    elif args.action == 'restore':
        # 恢复配置
        if not args.backup:
            print("❌ 请指定备份文件名: --backup <备份文件名>")
            print("   使用 'backup' 操作查看可用的备份文件")
            sys.exit(1)
        
        restore_config(args.backup)
    
    elif args.action == 'sample':
        # 创建示例配置
        create_sample_config()
    
    elif args.action == 'templates':
        # 列出配置模板
        list_templates()
    
    elif args.action == 'save-template':
        # 保存配置模板
        if not args.name:
            print("❌ 请指定模板名称: --name <模板名称>")
            sys.exit(1)
        if not args.file:
            print("❌ 请指定配置文件路径: --file <配置文件路径>")
            sys.exit(1)
        
        description = args.description or ""
        save_template(args.name, args.file, description)
    
    elif args.action == 'apply-template':
        # 应用配置模板
        if not args.name:
            print("❌ 请指定模板名称: --name <模板名称>")
            sys.exit(1)
        
        apply_template(args.name)
    
    elif args.action == 'delete-template':
        # 删除配置模板
        if not args.name:
            print("❌ 请指定模板名称: --name <模板名称>")
            sys.exit(1)
        
        delete_template(args.name)
    
    print("\n✨ 操作完成")

if __name__ == "__main__":
    main()
