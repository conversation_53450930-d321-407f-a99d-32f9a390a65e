# 快速安装版本 - 按依赖关系分组，避免冲突

# 基础依赖 - 优先安装
pillow==9.5.0
numpy==1.24.3
setuptools==68.0.0
wheel==0.40.0

# Web框架
flask==2.3.2
requests==2.31.0
werkzeug==2.3.6

# 文档处理
pdfplumber==0.9.0

# 深度学习框架 - 分开安装避免冲突
torch==2.0.1
torchvision==0.15.2

# Transformers
transformers==4.30.2
tokenizers==0.13.3

# PaddlePaddle生态 - 最后安装
paddlepaddle==2.5.1
paddleocr==2.7.0.3

# 图像处理
opencv-python-headless==4.8.0.74
shapely==2.0.1

# OCR备选
pytesseract==0.3.10
