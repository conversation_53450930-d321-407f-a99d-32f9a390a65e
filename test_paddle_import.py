#!/usr/bin/env python3
"""
测试PaddleOCR导入和基本功能
用于验证Docker容器中的PaddleOCR是否正确安装
"""

def test_paddle_imports():
    """测试PaddlePaddle和PaddleOCR的导入"""
    print("🔍 测试PaddlePaddle和PaddleOCR导入...")
    
    try:
        import paddle
        print(f"✅ PaddlePaddle导入成功，版本: {paddle.__version__}")
    except ImportError as e:
        print(f"❌ PaddlePaddle导入失败: {e}")
        return False
    
    try:
        from paddleocr import PaddleOCR
        print("✅ PaddleOCR导入成功")
    except ImportError as e:
        print(f"❌ PaddleOCR导入失败: {e}")
        return False
    
    return True

def test_ocr_initialization():
    """测试OCR实例化"""
    print("\n🔍 测试OCR实例化...")
    
    try:
        from paddleocr import PaddleOCR
        # 使用与main.py相同的配置
        ocr = PaddleOCR(
            lang='ch', 
            use_angle_cls=False, 
            det_limit_side_len=960
        )
        print("✅ OCR实例化成功")
        return True
    except Exception as e:
        print(f"❌ OCR实例化失败: {e}")
        return False

def test_other_dependencies():
    """测试其他相关依赖"""
    print("\n🔍 测试其他依赖...")
    
    dependencies = [
        ('PIL', 'Pillow'),
        ('cv2', 'OpenCV'),
        ('torch', 'PyTorch'),
        ('transformers', 'Transformers'),
        ('shapely', 'Shapely')
    ]
    
    all_success = True
    for module_name, display_name in dependencies:
        try:
            __import__(module_name)
            print(f"✅ {display_name}导入成功")
        except ImportError as e:
            print(f"❌ {display_name}导入失败: {e}")
            all_success = False
    
    return all_success

if __name__ == "__main__":
    print("=" * 50)
    print("PaddleOCR环境测试")
    print("=" * 50)
    
    success = True
    
    # 测试导入
    if not test_paddle_imports():
        success = False
    
    # 测试OCR初始化
    if not test_ocr_initialization():
        success = False
    
    # 测试其他依赖
    if not test_other_dependencies():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 所有测试通过！PaddleOCR环境配置正确")
    else:
        print("⚠️  部分测试失败，请检查依赖安装")
    print("=" * 50)
