stages:
  - build

variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: ""
  DOCKER_BUILDKIT: 1

# Build and push Docker image
docker-build-push:
  stage: build
  image: docker:latest
  timeout: 90m  # 减少超时时间，配合优化的Dockerfile
  tags:
    - cloud-dev
  services:
    - name: docker:dind
      alias: docker
      command: ["--insecure-registry=$HARBOR_REGISTRY"]
  before_script:
    # Configure BuildKit for insecure registry
    - mkdir -p ~/.docker
    - |
      cat > ~/.docker/config.json << EOF
      {
        "experimental": "enabled",
        "insecure-registries": ["$HARBOR_REGISTRY"]
      }
      EOF
    # Login to Harbor registry
    - echo $HARBOR_PASSWORD | docker login $HARBOR_REGISTRY -u $HARBOR_USERNAME --password-stdin

    # 尝试拉取缓存镜像加速构建
    - docker pull $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:latest || true
  script:
    # Build Docker image with cache
    - |
      docker build \
        --cache-from $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:latest \
        -t $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:$CI_COMMIT_SHORT_SHA .
    
    # Tag the same image as latest
    - docker tag $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:$CI_COMMIT_SHORT_SHA $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:latest
    
    # Push both tags to insecure registry
    - docker push $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:$CI_COMMIT_SHORT_SHA
    - docker push $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:latest
    
    # Output success message
    - echo "Successfully pushed $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:$CI_COMMIT_SHORT_SHA"
    - echo "Successfully pushed $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:latest"

  after_script:
    # 清理构建缓存释放空间
    - docker system prune -f || true

  retry:
    max: 2
    when:
      - runner_system_failure
      - stuck_or_timeout_failure

  only:
    - dev
    - main