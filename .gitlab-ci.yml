stages:
  - build
  - test
  - deploy

variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: ""
  DOCKER_BUILDKIT: 1
  # 优化构建性能
  BUILDKIT_PROGRESS: plain
  DOCKER_CLI_EXPERIMENTAL: enabled
  # 缓存配置
  CACHE_KEY: "$CI_COMMIT_REF_SLUG-$CI_COMMIT_SHORT_SHA"

# 构建阶段 - 优化版本
docker-build-push:
  stage: build
  image: docker:24-dind
  timeout: 90m  # 减少到90分钟，配合优化的Dockerfile
  tags:
    - cloud-dev
  services:
    - name: docker:24-dind
      alias: docker
      command: ["--insecure-registry=$HARBOR_REGISTRY", "--registry-mirror=https://docker.mirrors.ustc.edu.cn"]
  variables:
    # 启用BuildKit缓存
    DOCKER_BUILDKIT: 1
    BUILDKIT_INLINE_CACHE: 1
  before_script:
    # 显示构建信息
    - echo "🚀 开始构建 - 分支:$CI_COMMIT_REF_NAME, 提交:$CI_COMMIT_SHORT_SHA"
    - echo "📦 镜像名称:$HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME"

    # 配置BuildKit和Docker
    - mkdir -p ~/.docker
    - |
      cat > ~/.docker/config.json << EOF
      {
        "experimental": "enabled",
        "insecure-registries": ["$HARBOR_REGISTRY"],
        "registry-mirrors": ["https://docker.mirrors.ustc.edu.cn"],
        "features": {
          "buildkit": true
        }
      }
      EOF

    # 登录Harbor仓库
    - echo "🔐 登录Harbor仓库..."
    - echo $HARBOR_PASSWORD | docker login $HARBOR_REGISTRY -u $HARBOR_USERNAME --password-stdin

    # 尝试拉取缓存镜像
    - echo "📥 尝试拉取缓存镜像..."
    - docker pull $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:latest || true

  script:
    # 使用BuildKit构建镜像，启用缓存
    - echo "🔨 开始构建Docker镜像..."
    - |
      docker buildx build \
        --cache-from $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:latest \
        --cache-to type=inline \
        --build-arg BUILDKIT_INLINE_CACHE=1 \
        --tag $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:$CI_COMMIT_SHORT_SHA \
        --tag $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:latest \
        --push \
        .

    # 输出构建结果
    - echo "✅ 构建完成！"
    - echo "📦 镜像标签: $CI_COMMIT_SHORT_SHA, latest"
    - docker images | grep $HARBOR_IMAGE_NAME || true

  after_script:
    # 清理本地镜像释放空间
    - echo "🧹 清理构建缓存..."
    - docker system prune -f || true

  retry:
    max: 2
    when:
      - runner_system_failure
      - stuck_or_timeout_failure
      - api_failure

  rules:
    - if: $CI_COMMIT_BRANCH == "main"
      variables:
        IMAGE_TAG: "stable"
    - if: $CI_COMMIT_BRANCH == "dev"
      variables:
        IMAGE_TAG: "dev"
    - if: $CI_MERGE_REQUEST_IID
      variables:
        IMAGE_TAG: "mr-$CI_MERGE_REQUEST_IID"
    - when: never

# 镜像测试阶段
test-image:
  stage: test
  image: docker:24-dind
  timeout: 15m
  tags:
    - cloud-dev
  services:
    - name: docker:24-dind
      alias: docker
  dependencies:
    - docker-build-push
  before_script:
    - echo $HARBOR_PASSWORD | docker login $HARBOR_REGISTRY -u $HARBOR_USERNAME --password-stdin
  script:
    - echo "🧪 测试镜像功能..."
    - docker pull $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:$CI_COMMIT_SHORT_SHA

    # 启动容器测试
    - |
      container_id=$(docker run -d -p 5000:5000 \
        $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:$CI_COMMIT_SHORT_SHA)

    # 等待服务启动
    - echo "⏳ 等待服务启动..."
    - sleep 15

    # 健康检查
    - |
      if docker exec $container_id wget --spider -q http://localhost:5000/health; then
        echo "✅ 健康检查通过"
      else
        echo "❌ 健康检查失败"
        docker logs $container_id
        exit 1
      fi

    # 清理测试容器
    - docker stop $container_id
    - docker rm $container_id

  rules:
    - if: $CI_COMMIT_BRANCH == "main" || $CI_COMMIT_BRANCH == "dev"
    - if: $CI_MERGE_REQUEST_IID

# 部署到开发环境
deploy-dev:
  stage: deploy
  image: alpine:latest
  timeout: 10m
  tags:
    - cloud-dev
  dependencies:
    - test-image
  before_script:
    - apk add --no-cache curl jq
  script:
    - echo "🚀 部署到开发环境..."
    - echo "镜像: $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:$CI_COMMIT_SHORT_SHA"

    # 这里可以添加具体的部署逻辑，比如：
    # - 调用Kubernetes API
    # - 更新docker-compose文件
    # - 调用部署脚本等

    - echo "✅ 开发环境部署完成"

  environment:
    name: development
    url: http://dev.your-domain.com

  rules:
    - if: $CI_COMMIT_BRANCH == "dev"

# 部署到生产环境
deploy-prod:
  stage: deploy
  image: alpine:latest
  timeout: 10m
  tags:
    - cloud-dev
  dependencies:
    - test-image
  before_script:
    - apk add --no-cache curl jq
  script:
    - echo "🚀 部署到生产环境..."
    - echo "镜像: $HARBOR_REGISTRY/$HARBOR_PROJECT/$HARBOR_IMAGE_NAME:$CI_COMMIT_SHORT_SHA"

    # 生产环境部署逻辑
    - echo "✅ 生产环境部署完成"

  environment:
    name: production
    url: http://your-domain.com

  rules:
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual  # 生产环境需要手动触发

  allow_failure: false