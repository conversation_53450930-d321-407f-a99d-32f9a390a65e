# 临时文件和日志
temp_uploads
logs
*.log

# Python 字节码文件
__pycache__/
*.py[cod]
*$py.class
*.so

# 分发 / 打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 单元测试 / 覆盖率报告
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# 虚拟环境
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE 配置文件
.idea/
.vscode/
*.swp
*.swo
*~

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Flask
instance/
.webassets-cache

# 配置备份文件
config_backups/

# 机器学习模型文件（如果不需要版本控制）
# models/
# *.pkl
# *.joblib
# *.h5
# *.pb

# 系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 临时文件
*.tmp
*.temp
*.bak
*.backup

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 压缩文件
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip