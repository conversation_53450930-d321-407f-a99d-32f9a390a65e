# 快速构建版本的Dockerfile
FROM python:3.9-slim as builder

# 设置构建环境变量
ENV PIP_DISABLE_PIP_VERSION_CHECK=1
ENV PIP_NO_CACHE_DIR=1
ENV PYTHONUNBUFFERED=1

# 使用中国镜像源
RUN sed -i 's|deb.debian.org|mirrors.aliyun.com|g' /etc/apt/sources.list.d/debian.sources && \
    sed -i 's|security.debian.org|mirrors.aliyun.com|g' /etc/apt/sources.list.d/debian.sources

# 安装构建依赖
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    libgl1-mesa-dev \
    libglib2.0-0 \
    libjpeg62-turbo-dev \
    libpng-dev \
    libfreetype6-dev \
    wget \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 配置pip镜像源
RUN pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple/ && \
    pip config set global.extra-index-url "https://pypi.douban.com/simple/ https://mirrors.aliyun.com/pypi/simple/" && \
    pip config set global.timeout 1200 && \
    pip config set global.retries 10 && \
    pip config set global.trusted-host "pypi.tuna.tsinghua.edu.cn pypi.douban.com mirrors.aliyun.com"

# 升级pip和安装基础工具
RUN pip install --upgrade pip setuptools wheel

# 创建虚拟环境
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# 复制requirements文件
COPY requirements-fast.txt /tmp/requirements-fast.txt

# 分批安装依赖，避免超时
RUN pip install --timeout=1200 --retries=10 \
    pillow==9.5.0 \
    numpy==1.24.3 \
    flask==2.3.2 \
    requests==2.31.0 \
    pdfplumber==0.9.0

RUN pip install --timeout=1200 --retries=10 \
    torch==2.0.1 \
    torchvision==0.15.2

RUN pip install --timeout=1200 --retries=10 \
    transformers==4.30.2 \
    tokenizers==0.13.3

RUN pip install --timeout=1200 --retries=10 \
    paddlepaddle==2.5.1

RUN pip install --timeout=1200 --retries=10 \
    paddleocr==******* \
    opencv-python-headless==******** \
    shapely==2.0.1 \
    pytesseract==0.3.10

# 运行时镜像
FROM python:3.9-slim

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV FLASK_APP=structured_report/api.py
ENV FLASK_ENV=production
ENV PATH="/opt/venv/bin:$PATH"

# 使用中国镜像源
RUN sed -i 's|deb.debian.org|mirrors.aliyun.com|g' /etc/apt/sources.list.d/debian.sources && \
    sed -i 's|security.debian.org|mirrors.aliyun.com|g' /etc/apt/sources.list.d/debian.sources

# 安装运行时依赖
RUN apt-get update && apt-get install -y --no-install-recommends \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    libjpeg62-turbo \
    libpng16-16 \
    libfreetype6 \
    wget \
    && rm -rf /var/lib/apt/lists/*

# 从构建阶段复制虚拟环境
COPY --from=builder /opt/venv /opt/venv

# 设置工作目录
WORKDIR /app

# 复制应用代码
COPY . .

# 创建必要目录
RUN mkdir -p temp_uploads logs

# 设置权限
RUN chmod +x run_api.py

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:5000/health || exit 1

# 暴露端口
EXPOSE 5000

# 启动命令
CMD ["python", "structured_report/api.py"]
