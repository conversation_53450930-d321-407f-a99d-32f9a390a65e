#!/usr/bin/env python3
"""
HTTP API服务启动脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from structured_report.api import app

if __name__ == '__main__':
    print("启动结构化医学报告提取与隐私保护 HTTP API 服务")
    print("📋 可用接口:")
    print("  GET  /health                 - 健康检查")
    print("  POST /extract                - 文档结构化提取")
    print("  POST /modify                 - 字段值修改")
    print("  POST /mosaic                 - 马赛克处理")
    print("  POST /process                - 综合处理")
    print("  GET  /download/<task_id>/<filename> - 文件下载")
    print("  DELETE /cleanup/<task_id>    - 清理临时文件")
    print()
    print("服务地址: http://localhost:5000")
    print("API文档: 请查看 README.md")
    print()
    app.run(host='0.0.0.0', port=5000, debug=True)